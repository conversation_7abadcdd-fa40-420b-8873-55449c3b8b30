<x-app-layout>
    <x-slot name="header">
        {{ $data ?? null ? 'Edit' : 'Tambah' }} <PERSON><PERSON><PERSON><PERSON>
    </x-slot>
    <x-slot name="headerRight">
        <div class="page-header-right-items">
            <div class="d-flex align-items-center gap-2 page-header-right-items-wrapper">
                <a href="{{ route('attendance-request.index') }}" class="btn btn-light-brand">
                    <i class="feather-x me-2"></i>
                    <span>Batal</span>
                </a>
                <button class="btn btn-primary btnSubmit" style="width: 150px">
                    <i class="feather-save me-2"></i>
                    <span>Simpan</span>
                </button>
            </div>
        </div>
        <div class="d-md-none d-flex align-items-center">
            <a href="javascript:void(0)" class="page-header-right-open-toggle">
                <i class="feather-align-right fs-20"></i>
            </a>
        </div>
    </x-slot>
    
    <div class="row">
        <div class="col-12">
            <div class="card stretch stretch-full">
                <div class="card-body">
                    <x-validation-errors />
                    @if (session('error'))
                        <div class="alert alert-danger">
                            {{ session('error') }}
                        </div>
                    @endif
                    
                    <form class="needs-validation" novalidate 
                          action="{{ isset($data) ? route('attendance-request.update', $data->id) : route('attendance-request.store') }}" 
                          id="formSubmit" method="POST" enctype="multipart/form-data">
                        @csrf
                        @if (isset($data))
                            @method('PUT')
                        @endif
                        
                        <div class="row g-4">
                            <div class="col-md-6 mb-4">
                                <label class="form-label">Cabang <span class="text-danger">*</span></label>
                                @php
                                    $branches = App\Models\Branch::active()->pluck('name', 'id');
                                @endphp
                                <select class="form-control select2" name="branch_id" id="branch_id" required>
                                    <option value="">Pilih Cabang</option>
                                    @foreach ($branches as $key => $value)
                                        <option value="{{ $key }}" {{ old('branch_id', ($data->branch_id ?? null)) == $key ? 'selected' : '' }}>
                                            {{ $value }}
                                        </option>
                                    @endforeach
                                </select>
                                @error('branch_id')
                                    <div class="invalid-feedback d-block">{{ $message }}</div>
                                @enderror
                            </div>
                            
                            <div class="col-md-6 mb-4">
                                <label class="form-label">Nama Karyawan <span class="text-danger">*</span></label>
                                <select class="form-control select2" name="employee_user_id" id="employee_user_id" required disabled>
                                    <option value="">Pilih cabang terlebih dahulu</option>
                                </select>
                                @error('employee_user_id')
                                    <div class="invalid-feedback d-block">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                        
                        <div class="row g-4">
                            <div class="col-md-6 mb-4">
                                <label class="form-label">Tanggal Absen <span class="text-danger">*</span></label>
                                <input type="date" class="form-control" name="attendance_date" 
                                       value="{{ old('attendance_date', isset($data) ? $data->attendance_date->format('Y-m-d') : '') }}" 
                                       min="{{ date('Y-m-d') }}" required>
                                @error('attendance_date')
                                    <div class="invalid-feedback d-block">{{ $message }}</div>
                                @enderror
                            </div>
                            
                            <div class="col-md-6 mb-4">
                                <label class="form-label">Jenis Absen <span class="text-danger">*</span></label>
                                @php
                                    $attendanceTypes = App\Models\AttendanceTypeSetting::pluck('name', 'id');
                                @endphp
                                <select class="form-control select2" name="attendance_type_id" required>
                                    <option value="">Pilih Jenis Absen</option>
                                    @foreach ($attendanceTypes as $key => $value)
                                        <option value="{{ $key }}" {{ old('attendance_type_id', ($data->attendance_type_id ?? null)) == $key ? 'selected' : '' }}>
                                            {{ $value }}
                                        </option>
                                    @endforeach
                                </select>
                                @error('attendance_type_id')
                                    <div class="invalid-feedback d-block">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                        
                        <div class="row g-4">
                            <div class="col-md-12">
                                <label class="form-label">Keterangan/Alasan <span class="text-danger">*</span></label>
                                <textarea class="form-control" name="reason" rows="4" 
                                          placeholder="Masukkan keterangan atau alasan pengajuan absen..." required>{{ old('reason', ($data->reason ?? null)) }}</textarea>
                                @error('reason')
                                    <div class="invalid-feedback d-block">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                        
                        <div class="row mt-4">
                            <div class="col-md-12">
                                <div class="card border-info">
                                    <div class="card-header bg-info text-white">
                                        <h6 class="mb-0 text-white">
                                            <i class="feather-paperclip me-2"></i>
                                            Lampiran (Opsional)
                                        </h6>
                                    </div>
                                    <div class="card-body">
                                        @if(isset($data) && $data->attachment)
                                            <div class="mb-3">
                                                <div class="alert alert-info d-flex align-items-center">
                                                    <i class="feather-file me-2"></i>
                                                    <span>File saat ini: </span>
                                                    <a href="{{ asset($data->attachment) }}" target="_blank" class="ms-2 fw-bold">
                                                        {{ basename($data->attachment) }}
                                                    </a>
                                                </div>
                                            </div>
                                        @endif
                                        
                                        <input type="file" class="form-control" name="attachment" id="attachment"
                                               accept=".jpg,.jpeg,.png,.pdf,.doc,.docx">
                                        <small class="form-text text-muted mt-2 d-block">
                                            <i class="feather-info me-1"></i>
                                            Format yang diizinkan: JPG, JPEG, PNG, PDF, DOC, DOCX. Maksimal 2MB.
                                            @if(isset($data) && $data->attachment)
                                                <br><span class="text-warning">Biarkan kosong jika tidak ingin mengubah lampiran.</span>
                                            @endif
                                        </small>
                                        @error('attachment')
                                            <div class="invalid-feedback d-block mt-2">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        @if(isset($data))
                            <div class="row g-4">
                                <div class="col-md-12">
                                    <div class="card border-warning">
                                        <div class="card-header bg-warning text-dark">
                                            <h6 class="mb-0">
                                                <i class="feather-info me-2"></i>
                                                Informasi Pengajuan
                                            </h6>
                                        </div>
                                        <div class="card-body">
                                            <div class="row">
                                                <div class="col-md-3">
                                                    <strong>Status:</strong><br>
                                                    {!! $data->status_badge !!}
                                                </div>
                                                <div class="col-md-3">
                                                    <strong>Diajukan Oleh:</strong><br>
                                                    {{ $data->user->nama ?? '-' }}
                                                </div>
                                                <div class="col-md-3">
                                                    <strong>Tanggal Pengajuan:</strong><br>
                                                    {{ $data->created_at_formatted }}
                                                </div>
                                                <div class="col-md-3">
                                                    @if($data->approved_by)
                                                        <strong>Diproses Oleh:</strong><br>
                                                        {{ $data->approvedBy->nama ?? '-' }}<br>
                                                        <small class="text-muted">{{ $data->approved_at_formatted }}</small>
                                                    @else
                                                        <strong>Status:</strong><br>
                                                        <span class="text-warning">Menunggu Persetujuan</span>
                                                    @endif
                                                </div>
                                            </div>
                                            
                                            @if($data->rejection_reason)
                                                <div class="mt-3">
                                                    <strong class="text-danger">Alasan Penolakan:</strong><br>
                                                    <div class="text-danger">{{ $data->rejection_reason }}</div>
                                                </div>
                                            @endif
                                        </div>
                                    </div>
                                </div>
                            </div>
                        @endif
                    </form>
                </div>
            </div>
        </div>
    </div>

    @push('scripts')
    <script>
    $(document).ready(function() {
        // Initialize Select2
        $('.select2').select2({
            theme: 'bootstrap-5',
            width: '100%'
        });

        // Load employees when page loads (for edit mode)
        @if(isset($data))
            loadEmployees('{{ $data->branch_id }}', '{{ $data->employee_user_id }}');
        @endif
        
        // Load employees when branch is selected
        $('#branch_id').change(function() {
            let branchId = $(this).val();
            loadEmployees(branchId);
        });

        function loadEmployees(branchId, selectedEmployeeId = null) {
            let employeeSelect = $('#employee_user_id');
            
            if (branchId) {
                employeeSelect.prop('disabled', true).html('<option value="">Memuat...</option>');
                
                $.ajax({
                    url: "{{ route('attendance-request.getEmployeesByBranch') }}",
                    type: 'GET',
                    data: { branch_id: branchId },
                    success: function(response) {
                        employeeSelect.html('<option value="">Pilih Karyawan</option>');
                        
                        if (response.length > 0) {
                            $.each(response, function(index, employee) {
                                let selected = selectedEmployeeId && employee.id == selectedEmployeeId ? 'selected' : '';
                                employeeSelect.append(`<option value="${employee.id}" ${selected}>${employee.nama} (${employee.email})</option>`);
                            });
                            employeeSelect.prop('disabled', false);
                        } else {
                            employeeSelect.html('<option value="">Tidak ada karyawan aktif di cabang ini</option>');
                        }
                        
                        // Reinitialize Select2
                        employeeSelect.select2({
                            theme: 'bootstrap-5',
                            width: '100%'
                        });
                    },
                    error: function(xhr) {
                        employeeSelect.html('<option value="">Error memuat data karyawan</option>');
                        if (xhr.status === 403) {
                            Swal.fire('Error!', 'Anda tidak memiliki akses ke cabang ini', 'error');
                        } else {
                            Swal.fire('Error!', 'Terjadi kesalahan saat memuat data karyawan', 'error');
                        }
                    }
                });
            } else {
                employeeSelect.prop('disabled', true).html('<option value="">Pilih cabang terlebih dahulu</option>');
                employeeSelect.select2({
                    theme: 'bootstrap-5',
                    width: '100%'
                });
            }
        }

        // File upload preview
        $('#attachment').change(function() {
            let file = this.files[0];
            if (file) {
                let fileSize = file.size / 1024 / 1024; // Convert to MB
                let fileName = file.name;
                
                if (fileSize > 2) {
                    Swal.fire('Error!', 'Ukuran file tidak boleh lebih dari 2MB', 'error');
                    $(this).val('');
                    return;
                }
                
                // Show file info
                let fileInfo = `File dipilih: ${fileName} (${fileSize.toFixed(2)} MB)`;
                $(this).next('.form-text').html('<i class="feather-info me-1"></i>' + fileInfo);
            }
        });

        // Form submission
        $('.btnSubmit').click(function(e) {
            e.preventDefault();
            
            let form = $('#formSubmit')[0];
            if (form.checkValidity()) {
                // Show loading
                $(this).prop('disabled', true).html('<i class="feather-loader me-2"></i>Menyimpan...');
                form.submit();
            } else {
                form.classList.add('was-validated');
            }
        });
    });
    </script>
    @endpush
</x-app-layout>
