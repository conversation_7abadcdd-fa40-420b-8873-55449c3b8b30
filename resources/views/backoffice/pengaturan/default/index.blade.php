<x-app-layout class_main_content="p-0 d-flex">
    @include('backoffice.pengaturan.sidebar')
    <div class="content-area" data-scrollbar-target="#psScrollbarInit">
        <form action="{{ route('pengaturan.default.store') }}" method="POST">
            @csrf
            <div class="content-area-header bg-white sticky-top">
                <div class="page-header-left">
                </div>
                <div class="page-header-right ms-auto">
                    <div class="d-flex align-items-center gap-3 page-header-right-items-wrapper">
                        <button class="btn btn-primary">
                            <i class="feather-save me-2"></i>
                            <span>Si<PERSON>an <PERSON></span>
                        </button>
                    </div>
                </div>
            </div>
            <div class="content-area-body mb-5">
                <div class="card">
                    <div class="card-body">
                        <div class="mb-4">
                            <h4 class="fw-bold">Default Settings</h4>
                            <div class="fs-12 text-muted">Default setup setting</div>
                        </div>
                        {{-- <div class="mb-4">
                            <label class="form-label">Tinggi Kop Surat</label>
                            <div class="input-group mb-3">
                                <input type="number" class="form-control" placeholder="Masukan tinggi kop surat"
                                    name="height_kop_surat" value="{{ $setting['height_kop_surat'] ?? '' }}">
                                <span class="input-group-text">px</span>
                            </div>
                        </div>
                        <div class="mb-4">
                            <label class="form-label">Margin Surat</label>
                            <div class="row">
                                <div class="col-md-3">
                                    <label class="form-label">Margin Atas</label>
                                    <div class="input-group mb-3">
                                        <input type="number" class="form-control" placeholder=""
                                            name="surat_margin_top" value="{{ $setting['surat_margin_top'] ?? '' }}">
                                        <span class="input-group-text">cm</span>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <label class="form-label">Margin Bawah</label>
                                    <div class="input-group mb-3">
                                        <input type="number" class="form-control" placeholder=""
                                            name="surat_margin_bottom" value="{{ $setting['surat_margin_bottom'] ?? '' }}">
                                        <span class="input-group-text">cm</span>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <label class="form-label">Margin Kanan</label>
                                    <div class="input-group mb-3">
                                        <input type="number" class="form-control" placeholder=""
                                            name="surat_margin_right" value="{{ $setting['surat_margin_right'] ?? '' }}">
                                        <span class="input-group-text">cm</span>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <label class="form-label">Margin Kiri</label>
                                    <div class="input-group mb-3">
                                        <input type="number" class="form-control" placeholder=""
                                            name="surat_margin_left" value="{{ $setting['surat_margin_left'] ?? '' }}">
                                        <span class="input-group-text">cm</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="mb-4">
                            <label class="form-label">ILove PDF Public Key</label>
                            <input type="text" class="form-control" name="ilove_pdf_public_key"
                                placeholder="{{ isset($setting['ilove_pdf_public_key']) ? '********' : 'Masukkan ilove pdf public key' }}">
                            @if (isset($setting['ilove_pdf_public_key']))
                                <div class="fs-12 fw-bold text text-primary mt-1">ILove PDF public key sudah diisi</div>
                            @endif
                        </div>
                        <div class="mb-4">
                            <label class="form-label">ILove PDF Secret Key</label>
                            <input type="text" class="form-control" name="ilove_pdf_secret_key"
                                placeholder="{{ isset($setting['ilove_pdf_secret_key']) ? '********' : 'Masukkan ilove pdf secret key' }}">
                            @if (isset($setting['ilove_pdf_secret_key']))
                                <div class="fs-12 fw-bold text text-primary mt-1">ILove PDF secret key sudah diisi</div>
                            @endif
                        </div> --}}
                        {{-- <div class="mb-4">
                            <label class="form-label">Tipe eSign</label>
                            @php
                                $options = [
                                    'global' => 'Global',
                                    'psre' => 'PSrE',
                                ];
                            @endphp
                            <select class="form-select select2" name="type_esign">
                                @foreach ($options as $key => $value)
                                    <option value="{{ $key }}" {{ $key == ($setting['type_esign'] ?? '') ? 'selected' : '' }}>{{ $value }}</option>
                                @endforeach
                            </select>
                        </div> --}}
                    </div>
                </div>
            </div>
        </form>
    </div>

    @push('scripts')
        <script>
            $(document).ready(function() {

            });
        </script>
    @endpush

</x-app-layout>
