<?php

namespace App\Http\Controllers;

use App\Models\Role;
use Illuminate\Http\Request;
use Illuminate\Validation\Rule;
use App\Models\WorkingHourSetting;
use Illuminate\Support\Facades\DB;
use Yajra\DataTables\Facades\DataTables;

class WorkingHourSettingController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        return view('backoffice.working-hour-setting.index');
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $roles = Role::select('id', 'name')->orderBy('name')->get();
        $workingHourTypes = WorkingHourSetting::getWorkingHourTypes();
        return view('backoffice.working-hour-setting.create-update', compact('roles', 'workingHourTypes'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $id = $request->id;

        $validator = [
            'role_id' => [
                'required',
                'exists:roles,id',
                Rule::unique('working_hour_settings', 'role_id')->ignore($id)->whereNull('deleted_at'),
            ],
            'working_hour_type' => 'required|in:morning_shift,evening_shift,night_shift,full_day',
            'start_time' => 'nullable|date_format:H:i',
            'end_time' => 'nullable|date_format:H:i|after:start_time',
            'is_need_setting_day' => 'boolean',
        ];

        $request->validate($validator);

        DB::transaction(function () use ($request, $id) {
            $input = [
                'role_id' => $request->role_id,
                'working_hour_type' => $request->working_hour_type,
                'start_time' => $request->start_time,
                'end_time' => $request->end_time,
                'is_need_setting_day' => $request->boolean('is_need_setting_day'),
            ];

            WorkingHourSetting::updateOrCreate([
                'id' => $id,
            ], $input);
        });

        return redirect(route('working-hour-setting.index'))->with('success', 'Pengaturan jam kerja berhasil disimpan');
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        $data = WorkingHourSetting::with('role')->findOrFail($id);
        return view('backoffice.working-hour-setting.show', compact('data'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        $data = WorkingHourSetting::with('role')->findOrFail($id);
        $roles = Role::select('id', 'name')->orderBy('name')->get();
        $workingHourTypes = WorkingHourSetting::getWorkingHourTypes();
        return view('backoffice.working-hour-setting.create-update', compact('data', 'roles', 'workingHourTypes'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        try {
            $workingHourSetting = WorkingHourSetting::findOrFail($id);

            DB::transaction(function() use ($workingHourSetting) {
                $workingHourSetting->delete();
            });

            return response()->json(['status' => true,'message' => 'Data berhasil dihapus'], 200);

        } catch (\Throwable $th) {
           return response()->json(['status' => false,'message' => $th->getMessage()], 500);
        }
    }

    public function dataTable(Request $request)
    {
        $data = WorkingHourSetting::select(
            'working_hour_settings.id',
            'role_id',
            'working_hour_type',
            'start_time',
            'end_time',
            'is_need_setting_day',
            'working_hour_settings.created_at'
        )
        ->with('role:id,name')
        ->latest()
        ->filter($request);

        return DataTables::of($data)
            ->addIndexColumn()
            ->addColumn('role_name', function ($data) {
                return $data->role ? $data->role->name : '-';
            })
            ->addColumn('working_hour_type_badge', function ($data) {
                $badgeClass = 'bg-primary';
                return "<span class='badge {$badgeClass} px-3 py-2 rounded-full'>{$data->working_hour_type_name}</span>";
            })
            ->addColumn('time_range', function ($data) {
                if ($data->start_time && $data->end_time) {
                    return date('H:i', strtotime($data->start_time)) . ' - ' . date('H:i', strtotime($data->end_time));
                }
                return '-';
            })
            ->addColumn('need_setting_day_badge', function ($data) {
                $badgeClass = $data->is_need_setting_day ? 'bgsuccess' : 'bgsecondary';
                $text = $data->is_need_setting_day ? 'Ya' : 'Tidak';
                return "<span class='badge {$badgeClass} px-3 py-2'>{$text}</span>";
            })
            ->addColumn('action', function ($data) {
                $action_button = '';
                
                if(canPermission('Master Jam Kerja.Edit')) {
                    $action_button .= "<li>
                                            <a class='dropdown-item' href='".route('working-hour-setting.edit', $data->id)."'>
                                                <i class='feather feather-edit-3 me-3'></i>
                                                <span>Edit</span>
                                            </a>
                                        </li>";
                }

                if(canPermission('Master Jam Kerja.Delete')) {
                    $action_button .= " <li class='dropdown-divider'></li>
                                        <li>
                                            <a class='dropdown-item deleteData' href='javascript:void(0)'  data-id='$data->id' data-input='".json_encode($data)."'>
                                                <i class='feather feather-trash-2 me-3'></i>
                                                <span>Delete</span>
                                            </a>
                                        </li>";
                }

                $action = " <div class='hstack gap-2'>
                                                <div class='dropdown dropdown-overflow'>
                                                    <a href='javascript:void(0)'
                                                        class='avatar-text avatar-md btn-dropdown'
                                                        data-bs-toggle='dropdown' data-bs-offset='0,21'>
                                                        <i class='feather feather-more-horizontal'></i>
                                                    </a>
                                                    <ul class='dropdown-menu'>
                                                        $action_button
                                                    </ul>
                                                </div>
                                            </div>";
                return $action;
            })
            ->rawColumns(['action', 'working_hour_type_badge', 'need_setting_day_badge'])
            ->smart(true)
            ->make(true);
    }

    /**
     * Get list of working hour settings for select options
     */
    public function list(Request $request)
    {
        $settings = WorkingHourSetting::select('id', 'role_id', 'working_hour_type', 'start_time', 'end_time')
                                     ->with('role:id,name')
                                     ->when($request->role_id, function($q) use ($request) {
                                         $q->where('role_id', $request->role_id);
                                     })
                                     ->when($request->working_hour_type, function($q) use ($request) {
                                         $q->where('working_hour_type', $request->working_hour_type);
                                     })
                                     ->orderBy('created_at')
                                     ->get();
        
        return response()->json($settings);
    }
}
