<?php if (isset($component)) { $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54 = $attributes; } ?>
<?php $component = App\View\Components\AppLayout::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('app-layout'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(App\View\Components\AppLayout::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
     <?php $__env->slot('header', null, []); ?> 
        Pengajuan Absen
     <?php $__env->endSlot(); ?>
     <?php $__env->slot('headerRight', null, []); ?> 
     <?php $__env->endSlot(); ?>

    <div class="row">
        <div class="col-lg-12">
            <div class="card stretch stretch-full">
                <div class="card-body">
                    <div class="mb-4">
                        <div class="d-flex gap-2 align-items-center justify-content-between">
                            <a href="#" class="btn btn-outline-success" id="exportBtn">
                                <i class="feather-download me-2"></i>
                                <span>Export Excel</span>
                            </a>
                            <?php if(canPermission('Pengajuan Absen.Create')): ?>
                                <a href="<?php echo e(route('attendance-request.create')); ?>" class="btn btn-primary">
                                    <i class="feather-plus me-2"></i>
                                    <span>Tambah Pengajuan</span>
                                </a>
                            <?php endif; ?>
                        </div>
                    </div>
                    <div class="d-flex align-items-center flex-wrap">
                        <div class="" style="width: 300px">
                            <input type="text" class="form-control" id="search-input"
                                placeholder="Cari nama karyawan, cabang, keterangan..." oninput="handleSearch(event)"
                                style="width: 100%" />
                        </div>
                        <div class="ms-2" style="width: 150px">
                            <select class="form-select select2" id="branch-filter">
                                <option value="">Semua Cabang</option>
                                <?php $__currentLoopData = $branches; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $branch): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <option value="<?php echo e($branch->id); ?>"><?php echo e($branch->name); ?></option>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </select>
                        </div>
                        <div class="ms-2" style="width: 150px">
                            <select class="form-select select2" id="status-filter">
                                <option value="">Semua Status</option>
                                <option value="pending">Menunggu</option>
                                <option value="approved">Disetujui</option>
                                <option value="rejected">Ditolak</option>
                            </select>
                        </div>
                        <div class="ms-2" style="width: 150px">
                            <input type="date" class="form-control" id="date-from" placeholder="Tanggal Dari">
                        </div>
                        <div class="ms-2" style="width: 150px">
                            <input type="date" class="form-control" id="date-to" placeholder="Tanggal Sampai">
                        </div>
                    </div>

                    <div class="alert alert-info mt-3">
                        <h6 class="alert-heading">Informasi Pengajuan Absen:</h6>
                        <ul class="mb-0">
                            <li>Pengajuan absen dapat dibuat untuk karyawan di cabang yang dapat diakses</li>
                            <li>Status: <span class="badge bg-warning">Menunggu</span> <span
                                    class="badge bg-success">Disetujui</span> <span
                                    class="badge bg-danger">Ditolak</span></li>
                            <li>Pengajuan yang sudah diproses tidak dapat diedit atau dihapus</li>
                            <li>Lampiran bersifat opsional dan mendukung format JPG, PNG, PDF, DOC, DOCX</li>
                        </ul>
                    </div>

                    <div class="d-flex align-items-center gap-3 mt-4">
                        <div class="bulk-actions" style="display: none">
                            <button type="button" class="btn btn-danger btn-sm py-3" id="deleteSelected">
                                <i class="feather-trash-2 me-1"></i>
                                <span>Hapus Terpilih</span>
                            </button>
                        </div>
                    </div>

                    <div class="table-responsive mt-4">
                        <table class="table table-hover" id="example">
                            <thead>
                                <tr>
                                    <th style="width: 12px">
                                        <input type="checkbox" class="form-check-input" id="selectAll">
                                    </th>
                                    <th style="width: 12px">No</th>
                                    <th>Tanggal Pengajuan</th>
                                    <th>Nama Karyawan</th>
                                    <th>Cabang</th>
                                    <th>Tanggal Absen</th>
                                    <th>Jenis Absen</th>
                                    <th>Status</th>
                                    <th>Diajukan Oleh</th>
                                    <th>Aksi</th>
                                </tr>
                            </thead>
                            <tbody>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Rejection Modal -->
    <?php $__env->startPush('modals'); ?>
        <div class="modal fade" id="rejectModal" tabindex="-1" aria-labelledby="rejectModalLabel" aria-hidden="true">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="rejectModalLabel">Tolak Pengajuan Absen</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <form id="rejectForm">
                        <div class="modal-body">
                            <div class="mb-3">
                                <label for="rejection_reason" class="form-label">Alasan Penolakan <span
                                        class="text-danger">*</span></label>
                                <textarea class="form-control" id="rejection_reason" name="rejection_reason" rows="4"
                                    placeholder="Masukkan alasan penolakan..." required></textarea>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Batal</button>
                            <button type="submit" class="btn btn-danger">Tolak Pengajuan</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    <?php $__env->stopPush(); ?>

    <?php $__env->startPush('scripts'); ?>
        <?php echo $__env->make('libs.datatable', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
        <script>
            let table;
            let searchTimeout;

            function handleSearch(event) {
                clearTimeout(searchTimeout);
                searchTimeout = setTimeout(function() {
                    table.ajax.reload();
                }, 500);
            }

            $(document).ready(function() {
                table = $('#example').DataTable({
                    lengthMenu: [
                        [10, 25, 50, 100, 500, -1],
                        [10, 25, 50, 100, 500, "All"],
                    ],
                    searching: false,
                    responsive: false,
                    lengthChange: true,
                    autoWidth: false,
                    order: [],
                    pagingType: "full_numbers",
                    dom: '<"top"f>rt<"mt-4 d-flex align-items-center justify-content-between"ilp><"clear">',
                    language: {
                        search: "_INPUT_",
                        searchPlaceholder: "Cari...",
                        paginate: {
                            Search: '<i class="icon-search"></i>',
                            first: "<i class='fas fa-angle-double-left'></i>",
                            previous: "<i class='fas fa-angle-left'></i>",
                            next: "<i class='fas fa-angle-right'></i>",
                            last: "<i class='fas fa-angle-double-right'></i>",
                        },
                    },
                    oLanguage: {
                        sSearch: "",
                    },
                    processing: true,
                    serverSide: true,
                    ajax: {
                        url: "<?php echo e(route('attendance-request.dataTable')); ?>",
                        type: "POST",
                        data: function(d) {
                            d.keyword = $('#search-input').val();
                            d.branch_id = $('#branch-filter').val();
                            d.status = $('#status-filter').val();
                            d.date_from = $('#date-from').val();
                            d.date_to = $('#date-to').val();
                        }
                    },
                    columns: [{
                            data: 'checkbox',
                            name: 'checkbox',
                            orderable: false,
                            searchable: false
                        },
                        {
                            data: 'DT_RowIndex',
                            name: 'DT_RowIndex',
                            orderable: false,
                            searchable: false
                        },
                        {
                            data: 'created_at',
                            name: 'created_at'
                        },
                        {
                            data: 'employee_name',
                            name: 'employeeUser.nama'
                        },
                        {
                            data: 'branch_name',
                            name: 'branch.name'
                        },
                        {
                            data: 'attendance_date_formatted',
                            name: 'attendance_date'
                        },
                        {
                            data: 'attendance_type_name',
                            name: 'attendanceType.name'
                        },
                        {
                            data: 'status_badge',
                            name: 'status'
                        },
                        {
                            data: 'created_by',
                            name: 'user.nama'
                        },
                        {
                            data: 'action',
                            name: 'action',
                            orderable: false,
                            searchable: false
                        }
                    ],
                });

                // Initialize Select2
                $('.select2').select2({
                    theme: 'bootstrap-5',
                    width: '100%'
                });

                // Filter change events
                $('#branch-filter, #status-filter, #date-from, #date-to').change(function() {
                    table.ajax.reload();
                });

                // Export functionality
                $('#exportBtn').click(function(e) {
                    e.preventDefault();
                    let params = new URLSearchParams();

                    if ($('#search-input').val()) params.append('keyword', $('#search-input').val());
                    if ($('#branch-filter').val()) params.append('branch_id', $('#branch-filter').val());
                    if ($('#status-filter').val()) params.append('status', $('#status-filter').val());
                    if ($('#date-from').val()) params.append('date_from', $('#date-from').val());
                    if ($('#date-to').val()) params.append('date_to', $('#date-to').val());

                    window.location.href = "<?php echo e(route('attendance-request.exportExcel')); ?>?" + params.toString();
                });

                // Select all functionality
                $('#selectAll').change(function() {
                    $('.select-item').prop('checked', this.checked);
                    toggleBulkActions();
                });

                $(document).on('change', '.select-item', function() {
                    toggleBulkActions();
                });

                function toggleBulkActions() {
                    let checkedItems = $('.select-item:checked').length;
                    if (checkedItems > 0) {
                        $('.bulk-actions').show();
                    } else {
                        $('.bulk-actions').hide();
                    }
                }

                // Approve functionality
                $(document).on('click', '.approveData', function() {
                    let id = $(this).data('id');

                    Swal.fire({
                        title: 'Konfirmasi',
                        text: 'Apakah Anda yakin ingin menyetujui pengajuan ini?',
                        icon: 'question',
                        showCancelButton: true,
                        confirmButtonColor: '#28a745',
                        cancelButtonColor: '#6c757d',
                        confirmButtonText: 'Ya, Setujui',
                        cancelButtonText: 'Batal'
                    }).then((result) => {
                        if (result.value) {
                            $.ajax({
                                url: "<?php echo e(url('attendance-request')); ?>/" + id + "/approve",
                                type: 'POST',
                                data: {
                                    _token: "<?php echo e(csrf_token()); ?>"
                                },
                                success: function(response) {
                                    if (response.status) {
                                        Swal.fire('Berhasil!', response.message, 'success');
                                        table.ajax.reload();
                                    } else {
                                        Swal.fire('Error!', response.message, 'error');
                                    }
                                },
                                error: function(xhr) {
                                    Swal.fire('Error!',
                                        'Terjadi kesalahan saat memproses permintaan',
                                        'error');
                                }
                            });
                        }
                    });
                });

                // Reject functionality
                let rejectId = null;
                $(document).on('click', '.rejectData', function() {
                    rejectId = $(this).data('id');
                    $('#rejectModal').modal('show');
                });

                $('#rejectForm').submit(function(e) {
                    e.preventDefault();

                    $.ajax({
                        url: "<?php echo e(url('attendance-request')); ?>/" + rejectId + "/reject",
                        type: 'POST',
                        data: {
                            _token: "<?php echo e(csrf_token()); ?>",
                            rejection_reason: $('#rejection_reason').val()
                        },
                        success: function(response) {
                            if (response.status) {
                                $('#rejectModal').modal('hide');
                                $('#rejectForm')[0].reset();
                                Swal.fire('Berhasil!', response.message, 'success');
                                table.ajax.reload();
                            } else {
                                Swal.fire('Error!', response.message, 'error');
                            }
                        },
                        error: function(xhr) {
                            Swal.fire('Error!', 'Terjadi kesalahan saat memproses permintaan',
                                'error');
                        }
                    });
                });

                // Delete functionality
                $(document).on('click', '.deleteData', function() {
                    let id = $(this).data('id');

                    Swal.fire({
                        title: 'Konfirmasi',
                        text: 'Apakah Anda yakin ingin menghapus pengajuan ini?',
                        icon: 'warning',
                        showCancelButton: true,
                        confirmButtonColor: '#dc3545',
                        cancelButtonColor: '#6c757d',
                        confirmButtonText: 'Ya, Hapus',
                        cancelButtonText: 'Batal'
                    }).then((result) => {
                        if (result.value) {
                            $.ajax({
                                url: "<?php echo e(url('attendance-request')); ?>/" + id,
                                type: 'DELETE',
                                data: {
                                    _token: "<?php echo e(csrf_token()); ?>"
                                },
                                success: function(response) {
                                    if (response.status) {
                                        Swal.fire('Berhasil!', response.message, 'success');
                                        table.ajax.reload();
                                    } else {
                                        Swal.fire('Error!', response.message, 'error');
                                    }
                                },
                                error: function(xhr) {
                                    Swal.fire('Error!',
                                        'Terjadi kesalahan saat menghapus data', 'error'
                                    );
                                }
                            });
                        }
                    });
                });

                // Bulk delete functionality
                $('#deleteSelected').click(function() {
                    let selectedIds = [];
                    $('.select-item:checked').each(function() {
                        selectedIds.push($(this).val());
                    });

                    if (selectedIds.length === 0) {
                        Swal.fire('Peringatan!', 'Pilih minimal satu data untuk dihapus', 'warning');
                        return;
                    }

                    Swal.fire({
                        title: 'Konfirmasi',
                        text: `Apakah Anda yakin ingin menghapus ${selectedIds.length} pengajuan yang dipilih?`,
                        icon: 'warning',
                        showCancelButton: true,
                        confirmButtonColor: '#dc3545',
                        cancelButtonColor: '#6c757d',
                        confirmButtonText: 'Ya, Hapus',
                        cancelButtonText: 'Batal'
                    }).then((result) => {
                        if (result.value) {
                            $.ajax({
                                url: "<?php echo e(route('attendance-request.bulkDelete')); ?>",
                                type: 'POST',
                                data: {
                                    _token: "<?php echo e(csrf_token()); ?>",
                                    ids: selectedIds
                                },
                                success: function(response) {
                                    if (response.status) {
                                        Swal.fire('Berhasil!', response.message, 'success');
                                        table.ajax.reload();
                                        $('#selectAll').prop('checked', false);
                                        $('.bulk-actions').hide();
                                    } else {
                                        Swal.fire('Error!', response.message, 'error');
                                    }
                                },
                                error: function(xhr) {
                                    Swal.fire('Error!',
                                        'Terjadi kesalahan saat menghapus data', 'error'
                                    );
                                }
                            });
                        }
                    });
                });
            });
        </script>
    <?php $__env->stopPush(); ?>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $attributes = $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $component = $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php /**PATH /Users/<USER>/ProjectWork/hris-system/resources/views/backoffice/attendance-request/index.blade.php ENDPATH**/ ?>