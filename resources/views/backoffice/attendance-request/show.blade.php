<x-app-layout>
    <x-slot name="header">
        Detail Pengajuan Absen
    </x-slot>
    <x-slot name="headerRight">
        <div class="d-flex align-items-center gap-2">
            <a href="{{ route('attendance-request.index') }}" class="btn btn-secondary">
                <i class="feather-arrow-left me-2"></i>Kembali
            </a>

            @if ($attendanceRequest->canBeEdited() && canPermission('Pengajuan Absen.Update'))
                <a href="{{ route('attendance-request.edit', $attendanceRequest->id) }}" class="btn btn-warning">
                    <i class="feather-edit me-2"></i>
                    <span>Edit</span>
                </a>
            @endif

            @if ($attendanceRequest->canBeProcessed() && canPermission('Pengajuan Absen.Approve'))
                <button type="button" class="btn btn-success" id="approveBtn" data-id="{{ $attendanceRequest->id }}">
                    <i class="feather-check me-2"></i>
                    <span>Setujui</span>
                </button>
                <button type="button" class="btn btn-danger" id="rejectBtn" data-id="{{ $attendanceRequest->id }}">
                    <i class="feather-x me-2"></i>
                    <span>Tolak</span>
                </button>
            @endif
        </div>
    </x-slot>
    <div class="row">
        <div class="col-lg-8">
            <div class="card stretch stretch-full">
                <div class="card-header">
                    <h5 class="card-title">Informasi Pengajuan</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label fw-bold">Data Karyawan:</label>
                                <div class="form-control-plaintext">{{ $attendanceRequest->employeeUser->nama ?? '-' }}
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label fw-bold">Email Karyawan:</label>
                                <div class="form-control-plaintext">{{ $attendanceRequest->employeeUser->email ?? '-' }}
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label fw-bold">Cabang:</label>
                                <div class="form-control-plaintext">{{ $attendanceRequest->branch->name ?? '-' }}</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label fw-bold">Tanggal Absen:</label>
                                <div class="form-control-plaintext">{{ $attendanceRequest->attendance_date_formatted }}
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label fw-bold">Jenis Absen:</label>
                                <div class="form-control-plaintext">
                                    {{ $attendanceRequest->attendanceType->name ?? '-' }}</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label fw-bold">Status:</label>
                                <div class="form-control-plaintext">{!! $attendanceRequest->status_badge !!}</div>
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label class="form-label fw-bold">Keterangan/Alasan:</label>
                        <div class="form-control-plaintext border rounded p-3 bg-light">
                            {{ $attendanceRequest->reason }}
                        </div>
                    </div>

                    @if ($attendanceRequest->attachment)
                        <div class="mb-3">
                            <label class="form-label fw-bold">Lampiran:</label>
                            <div class="form-control-plaintext">
                                <div class="d-flex align-items-center">
                                    <i class="feather-paperclip me-2"></i>
                                    <a href="{{ asset($attendanceRequest->attachment) }}" target="_blank"
                                        class="text-primary">
                                        {{ basename($attendanceRequest->attachment) }}
                                    </a>
                                    <span class="badge bg-info ms-2">
                                        {{ strtoupper(pathinfo($attendanceRequest->attachment, PATHINFO_EXTENSION)) }}
                                    </span>
                                </div>
                            </div>
                        </div>
                    @endif

                    @if ($attendanceRequest->rejection_reason)
                        <div class="mb-3">
                            <label class="form-label fw-bold text-danger">Alasan Penolakan:</label>
                            <div class="form-control-plaintext border rounded p-3 bg-danger-subtle text-danger">
                                {{ $attendanceRequest->rejection_reason }}
                            </div>
                        </div>
                    @endif
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <div class="card stretch stretch-full">
                <div class="card-header">
                    <h5 class="card-title">Riwayat Pengajuan</h5>
                </div>
                <div class="card-body">
                    <div class="timeline">
                        <div class="timeline-item mb-4">
                            <div class="timeline-marker bg-primary"></div>
                            <div class="timeline-content">
                                <h6 class="timeline-title">Pengajuan Dibuat</h6>
                                <p class="timeline-text">
                                    Oleh: {{ $attendanceRequest->user->nama ?? '-' }}<br>
                                    Tanggal: {{ $attendanceRequest->created_at_formatted }}
                                </p>
                            </div>
                        </div>

                        @if ($attendanceRequest->approved_by)
                            <div class="timeline-item">
                                <div
                                    class="timeline-marker {{ $attendanceRequest->status === 'approved' ? 'bg-success' : 'bg-danger' }}">
                                </div>
                                <div class="timeline-content">
                                    <h6 class="timeline-title">
                                        {{ $attendanceRequest->status === 'approved' ? 'Pengajuan Disetujui' : 'Pengajuan Ditolak' }}
                                    </h6>
                                    <p class="timeline-text">
                                        Oleh: {{ $attendanceRequest->approvedBy->nama ?? '-' }}<br>
                                        Tanggal: {{ $attendanceRequest->approved_at_formatted }}
                                    </p>
                                </div>
                            </div>
                        @endif
                    </div>

                    @if ($attendanceRequest->status === 'pending')
                        <div class="alert alert-warning">
                            <h6 class="alert-heading">Status Menunggu</h6>
                            <p class="mb-0">Pengajuan ini masih menunggu persetujuan dari atasan.</p>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>

    <!-- Rejection Modal -->
    @push('modals')
        <div class="modal fade" id="rejectModal" tabindex="-1" aria-labelledby="rejectModalLabel" aria-hidden="true">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="rejectModalLabel">Tolak Pengajuan Absen</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <form id="rejectForm">
                        <div class="modal-body">
                            <div class="mb-3">
                                <label for="rejection_reason" class="form-label">Alasan Penolakan <span
                                        class="text-danger">*</span></label>
                                <textarea class="form-control" id="rejection_reason" name="rejection_reason" rows="4"
                                    placeholder="Masukkan alasan penolakan..." required></textarea>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Batal</button>
                            <button type="submit" class="btn btn-danger">Tolak Pengajuan</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    @endpush

    @push('scripts')
    <script>
        $(document).ready(function() {
            // Approve functionality
            $('#approveBtn').click(function() {
                let id = $(this).data('id');

                Swal.fire({
                    title: 'Konfirmasi',
                    text: 'Apakah Anda yakin ingin menyetujui pengajuan ini?',
                    icon: 'question',
                    showCancelButton: true,
                    confirmButtonColor: '#28a745',
                    cancelButtonColor: '#6c757d',
                    confirmButtonText: 'Ya, Setujui',
                    cancelButtonText: 'Batal'
                }).then((result) => {
                    if (result.value) {
                        $.ajax({
                            url: "{{ url('attendance-request') }}/" + id + "/approve",
                            type: 'POST',
                            data: {
                                _token: "{{ csrf_token() }}"
                            },
                            success: function(response) {
                                if (response.status) {
                                    Swal.fire('Berhasil!', response.message, 'success')
                                        .then(() => {
                                            location.reload();
                                        });
                                } else {
                                    Swal.fire('Error!', response.message, 'error');
                                }
                            },
                            error: function(xhr) {
                                Swal.fire('Error!',
                                    'Terjadi kesalahan saat memproses permintaan',
                                    'error');
                            }
                        });
                    }
                });
            });

            // Reject functionality
            $('#rejectBtn').click(function() {
                $('#rejectModal').modal('show');
            });

            $('#rejectForm').submit(function(e) {
                e.preventDefault();
                let id = $('#rejectBtn').data('id');

                $.ajax({
                    url: "{{ url('attendance-request') }}/" + id + "/reject",
                    type: 'POST',
                    data: {
                        _token: "{{ csrf_token() }}",
                        rejection_reason: $('#rejection_reason').val()
                    },
                    success: function(response) {
                        if (response.status) {
                            $('#rejectModal').modal('hide');
                            Swal.fire('Berhasil!', response.message, 'success').then(() => {
                                location.reload();
                            });
                        } else {
                            Swal.fire('Error!', response.message, 'error');
                        }
                    },
                    error: function(xhr) {
                        Swal.fire('Error!', 'Terjadi kesalahan saat memproses permintaan',
                            'error');
                    }
                });
            });

            // Delete functionality
            $('#deleteBtn').click(function() {
                let id = $(this).data('id');

                Swal.fire({
                    title: 'Konfirmasi',
                    text: 'Apakah Anda yakin ingin menghapus pengajuan ini?',
                    icon: 'warning',
                    showCancelButton: true,
                    confirmButtonColor: '#dc3545',
                    cancelButtonColor: '#6c757d',
                    confirmButtonText: 'Ya, Hapus',
                    cancelButtonText: 'Batal'
                }).then((result) => {
                    if (result.value) {
                        $.ajax({
                            url: "{{ url('attendance-request') }}/" + id,
                            type: 'DELETE',
                            data: {
                                _token: "{{ csrf_token() }}"
                            },
                            success: function(response) {
                                if (response.status) {
                                    Swal.fire('Berhasil!', response.message, 'success')
                                        .then(() => {
                                            window.location.href =
                                                "{{ route('attendance-request.index') }}";
                                        });
                                } else {
                                    Swal.fire('Error!', response.message, 'error');
                                }
                            },
                            error: function(xhr) {
                                Swal.fire('Error!',
                                    'Terjadi kesalahan saat menghapus data', 'error'
                                    );
                            }
                        });
                    }
                });
            });
        });
    </script>
@endpush
</x-app-layout>
