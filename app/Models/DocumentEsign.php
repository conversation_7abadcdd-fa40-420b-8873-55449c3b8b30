<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class DocumentEsign extends Model
{
    use HasFactory, HasUuids;
    protected $guarded = ['id'];

    public function signers()
    {
        return $this->hasMany(SignerDocumentEsign::class);
    }

    public function annotations()
    {
        return $this->hasMany(AnnotationDocumentEsign::class);
    }
}
