<x-app-layout>
    <x-slot name="header">
        {{ $data ?? null ? 'Edit' : 'Tambah' }} Pengguna
    </x-slot>
    <x-slot name="headerRight">
        <div class="page-header-right-items">
            <div class="d-flex align-items-center gap-2 page-header-right-items-wrapper">
                <a href="{{ route('pengguna.index') }}" class="btn btn-light-brand">
                    <i class="feather-x me-2"></i>
                    <span>
                        Batal
                    </span>
                </a>
                <button class="btn btn-primary btnSubmit" style="width: 150px">
                    <i class="feather-save me-2"></i>
                    <span>Simpan</span>
                </button>
            </div>
        </div>
        <div class="d-md-none d-flex align-items-center">
            <a href="javascript:void(0)" class="page-header-right-open-toggle">
                <i class="feather-align-right fs-20"></i>
            </a>
        </div>
    </x-slot>
    <div class="row">
        <div class="col-12">
            <div class="card stretch stretch-full">
                <div class="card-body">
                    <x-validation-errors />
                    @if (session('error'))
                        <div class="alert alert-danger">
                            {{ session('error') }}
                        </div>
                    @endif
                    <form
                        class="needs-validation"
                        novalidate
                        action="{{ route('pengguna.store') }}"
                        id="formSubmit" method="POST">
                        @csrf
                        @if ($data ?? false)
                            <input type="hidden" name="id" value="{{ $data->id }}">
                        @endif
                        <div class="row g-4">
                            <div class="col-md-4">
                                <label class="form-label">Nama</label>
                                <input type="text" class="form-control" name="nama" placeholder="Nama" required value="{{ old('nama', ($data->nama ?? null)) }}">
                            </div>
                            <div class="col-md-4">
                                <label class="form-label">Email</label>
                                <input type="email" class="form-control" name="email" placeholder="Email" required value="{{ old('email', ($data->email ?? null)) }}">
                            </div>
                            <div class="col-md-4">
                                <label class="form-label">Role</label>
                                @php
                                    $role = App\Models\Role::pluck('name', 'id');
                                @endphp
                                <select class="form-control select2" name="role_id" data-select2-selector="icon">
                                    <option value="">Pilih Role</option>
                                    @foreach ($role as $key => $value)
                                        <option value="{{ $key }}" {{ old('role_id', ($data->role_id ?? null)) == $key ? 'selected' : '' }}>{{ $value }}</option>
                                    @endforeach
                                </select>
                            </div>
                        </div>
                        <div class="row g-4">
                            @php
                                $branches = App\Models\Branch::active()->pluck('name', 'id');
                                $branch_ids = isset($data) ? ($data->userAccessBranches->pluck('branch_id')->toArray() ?? []) : [];
                                $branch_ids = old('branch_ids', $branch_ids);

                                // Debug information (remove in production)
                                if(isset($data)) {
                                    \Log::info('User Edit - Branch IDs:', $branch_ids);
                                    \Log::info('User Access Branches:', $data->userAccessBranches->toArray());
                                }
                            @endphp
                            <div class="col-md-12">
                                <div class="card border-primary mt-4">
                                    <div class="card-header bg-primary text-white">
                                        <h6 class="mb-0 text-white">
                                            <i class="feather-map-pin me-2"></i>
                                            Akses Cabang <span class="text-warning">*</span>
                                        </h6>
                                    </div>
                                    <div class="card-body">
                                        <select class="form-select select2" name="branch_ids[]" id="branch_ids" multiple required>
                                            @foreach ($branches as $key => $value)
                                                <option value="{{ $key }}" {{ in_array($key, $branch_ids) ? 'selected' : '' }}>{{ $value }}</option>
                                            @endforeach
                                        </select>
                                        <small class="form-text text-muted mt-2 d-block">
                                            <i class="feather-info me-1"></i>
                                            Pilih satu atau lebih cabang yang dapat diakses oleh pengguna ini. Pengguna hanya dapat mengakses data dari cabang yang dipilih.
                                        </small>
                                        @error('branch_ids')
                                            <div class="invalid-feedback d-block mt-2">{{ $message }}</div>
                                        @enderror

                                        @if(isset($data) && count($branch_ids) > 0)
                                            <div class="mt-2">
                                                <small class="text-success">
                                                    <i class="feather-check-circle me-1"></i>
                                                    Saat ini memiliki akses ke {{ count($branch_ids) }} cabang
                                                </small>
                                            </div>
                                        @endif
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row g-4">
                            <div class="col-md-4">
                                <label class="form-label">Password</label>
                                <input type="password" class="form-control" name="password"
                                    placeholder="Masukan Password" {{ isset($data) ? '' : 'required' }}>
                                @if(isset($data))
                                    <small class="form-text text-muted">Kosongkan jika tidak ingin mengubah password</small>
                                @endif
                            </div>
                            <div class="col-md-4">
                                <label class="form-label">Konfirmasi Password</label>
                                <input type="password" class="form-control" name="confirm_password"
                                    placeholder="Masukan Konfirmasi Password">
                            </div>
                            {{-- <div class="col-md-8">
                                <label class="form-label
                                ">Tanda Tangan</label>
                                <div id="signature-left" class="signature border overflow-hidden">
                                    <canvas width="700" height="250" style="touch-action: none;"
                                        id="signatureCanvas"></canvas><br />
                                    <textarea id="signatureLeft64" name="ttd_pihak_pemohon_penggugat" style="display:none"></textarea>
                                </div>
                                <div>
                                    <button type="button" class="btn signature-clear  float-end mt-2"
                                        style="font-weight: 400; cursor:pointer; background: #ffc107; padding: 0; font-size: 12px; vertical-align: middle"><i
                                            class="fas fa-eraser"></i>&nbsp;Ulangi
                                        tanda tangan</button>
                                </div>
                            </div> --}}
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
    @push('scripts')
        <script>
            $(document).ready(function() {
                // Initialize Select2 for branch selection with enhanced options
                $('#branch_ids').select2({
                    theme: 'bootstrap-5',
                    width: '100%',
                    placeholder: 'Pilih cabang yang dapat diakses...',
                    allowClear: false,
                    closeOnSelect: false,
                    tags: false
                });

                // Form submission handler
                $('.btnSubmit').on('click', function() {
                    // Validate branch selection
                    const selectedBranches = $('#branch_ids').val();
                    if (!selectedBranches || selectedBranches.length === 0) {
                        Swal.fire({
                            icon: 'error',
                            title: 'Akses Cabang Diperlukan!',
                            text: 'Silakan pilih minimal satu cabang yang dapat diakses oleh pengguna ini.'
                        });
                        return false;
                    }

                    $('#formSubmit').submit();
                });
            });
        </script>
    @endpush
</x-app-layout>
