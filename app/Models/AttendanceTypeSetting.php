<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class AttendanceTypeSetting extends Model
{
    use HasFactory, HasUuids, SoftDeletes;

    protected $guarded = ['id'];

    protected $casts = [
        'reduction_days_leave' => 'integer',
        'reduction_days_off' => 'integer',
        'additional_days' => 'integer',
    ];

    protected $appends = ['reduction_leave_formatted', 'reduction_off_formatted', 'additional_formatted'];

    public function getReductionLeaveFormattedAttribute()
    {
        return $this->reduction_days_leave . ' hari';
    }

    public function getReductionOffFormattedAttribute()
    {
        return $this->reduction_days_off . ' hari';
    }

    public function getAdditionalFormattedAttribute()
    {
        return $this->additional_days . ' hari';
    }

    public function scopeFilter($query, $request)
    {
        return $query->when($request->keyword, function ($q, $filter) {
            $q->where('name', 'like', '%' . $filter . '%')
                ->orWhere('code', 'like', '%' . $filter . '%');
        });
    }

    public function scopeWithReduction($query)
    {
        return $query->where(function ($q) {
            $q->where('reduction_days_leave', '>', 0)
                ->orWhere('reduction_days_off', '>', 0);
        });
    }

    public function scopeWithAddition($query)
    {
        return $query->where('additional_days', '>', 0);
    }
}
