<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class AttachmentFileSurat extends Model
{
    use HasFactory, HasUuids;
    protected $guarded = ['id'];

    public function tandaTangan()
    {
        return $this->morphMany(TandaTanganSurat::class, 'referable', 'referable_type', 'referable_id');
    }
}
