<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('leave_off_settings', function (Blueprint $table) {
            $table->uuid('id');
            $table->string('name')->unique();
            $table->string('code')->unique();
            $table->enum('leave_type', ['cutting', 'non-cutting']);
            $table->text('min_service_length')->nullable();
            $table->bigInteger('max_leave_days')->default(0);
            $table->softDeletes();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('leave_off_settings');
    }
};
