<?php

namespace App\Http\Controllers;

use App\Models\Employee;
use App\Models\Role;
use App\Models\Branch;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Yajra\DataTables\Facades\DataTables;

class EmployeeController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        return view('backoffice.employee.index');
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return view('backoffice.employee.create-update');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'nama' => 'required|string|max:255',
            'kode' => 'nullable|string|max:255|unique:employees,kode',
            'role_id' => 'nullable|exists:roles,id',
            'branch_id' => 'nullable|exists:branches,id',
            'join_date' => 'nullable|date',
            'tanggal_lahir' => 'nullable|date',
            'gender' => 'nullable|in:male,female',
            'status' => 'required|in:active,inactive,resigned',
            'hari_off' => 'nullable|array',
            'hari_off.*' => 'in:monday,tuesday,wednesday,thursday,friday,saturday,sunday',
            'email' => 'nullable|email|unique:employees,email',
            'no_rekening' => 'nullable|string|max:255',
            'bank' => 'nullable|string|max:255',
            'deposit_seragam' => 'nullable|numeric|min:0',
            'pengembalian_seragam' => 'nullable|numeric|min:0',
            'jumlah_cuti_tahunan' => 'nullable|integer|min:0',
            'masa_kerja_hari' => 'nullable|integer|min:0',
            'jumlah_seragam' => 'nullable|integer|min:0',
        ]);

        try {
            DB::transaction(function () use ($request) {
                $data = $request->all();
                
                // Handle hari_off array
                if ($request->has('hari_off') && is_array($request->hari_off)) {
                    $data['hari_off'] = $request->hari_off;
                } else {
                    $data['hari_off'] = null;
                }

                if ($request->has('id') && $request->id) {
                    // Update existing employee
                    $employee = Employee::findOrFail($request->id);
                    $employee->update($data);
                } else {
                    // Create new employee
                    Employee::create($data);
                }
            });

            return redirect()->route('employee.index')->with('success', 'Data karyawan berhasil disimpan');
        } catch (\Throwable $th) {
            return back()->withInput()->with('error', 'Terjadi kesalahan: ' . $th->getMessage());
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        $employee = Employee::with(['role', 'branch'])->findOrFail($id);
        return view('backoffice.employee.show', compact('employee'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        $data = Employee::findOrFail($id);
        return view('backoffice.employee.create-update', compact('data'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        $request->merge(['id' => $id]);
        return $this->store($request);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        try {
            $employee = Employee::findOrFail($id);
            $employee->delete();

            return response()->json(['status' => true, 'message' => 'Data berhasil dihapus'], 200);
        } catch (\Throwable $th) {
            return response()->json(['status' => false, 'message' => $th->getMessage()], 500);
        }
    }

    public function dataTable(Request $request)
    {
        $data = Employee::select(
            'id',
            'nama',
            'kode',
            'role_id',
            'branch_id',
            'join_date',
            'status',
            'gender',
            'hari_off'
        )
            ->with([
                'role:id,name',
                'branch:id,name,code'
            ])
            ->latest()
            ->filter($request);

        return DataTables::of($data)
            ->addIndexColumn()
            ->addColumn('role_name', function ($data) {
                return $data->role ? $data->role->name : '-';
            })
            ->addColumn('branch_name', function ($data) {
                return $data->branch ? $data->branch->name : '-';
            })
            ->addColumn('formatted_join_date', function ($data) {
                return $data->formatted_join_date;
            })
            ->addColumn('status_badge', function ($data) {
                return $data->status_badge;
            })
            ->addColumn('gender_label', function ($data) {
                return $data->gender_label;
            })
            ->addColumn('hari_off_formatted', function ($data) {
                return $data->hari_off_formatted;
            })
            ->addColumn('action', function ($data) {
                $action_button = '';
                
                $action_button .= "<li>
                                        <a class='dropdown-item' href='" . route('employee.show', $data->id) . "'>
                                            <i class='feather feather-eye me-3'></i>
                                            <span>Detail</span>
                                        </a>
                                    </li>";
                
                $action_button .= "<li>
                                        <a class='dropdown-item' href='" . route('employee.edit', $data->id) . "'>
                                            <i class='feather feather-edit-3 me-3'></i>
                                            <span>Edit</span>
                                        </a>
                                    </li>";

                $action_button .= " <li class='dropdown-divider'></li>
                                    <li>
                                        <a class='dropdown-item deleteData' href='javascript:void(0)'  data-id='$data->id' data-input='" . json_encode($data) . "'>
                                            <i class='feather feather-trash-2 me-3'></i>
                                            <span>Delete</span>
                                        </a>
                                    </li>";

                return "<div class='dropdown'>
                            <a href='#' class='btn btn-outline-light btn-size-sm btn-icon' data-bs-toggle='dropdown' aria-expanded='false'>
                                <i class='feather feather-more-horizontal'></i>
                            </a>
                            <ul class='dropdown-menu dropdown-menu-end'>
                                $action_button
                            </ul>
                        </div>";
            })
            ->rawColumns(['status_badge', 'action'])
            ->make(true);
    }
}
