<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\LeaveOffSetting;
use Illuminate\Validation\Rule;
use Illuminate\Support\Facades\DB;
use App\Http\Controllers\Controller;
use Yajra\DataTables\Facades\DataTables;

class LeaveOffSettingController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        return view('backoffice.leave-off-setting.index');
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $leaveTypes = LeaveOffSetting::getLeaveTypes();
        return view('backoffice.leave-off-setting.create-update', compact('leaveTypes'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $id = $request->id;

        $validator = [
            'name' => [
                'required',
                'string',
                'max:255',
                Rule::unique('leave_off_settings', 'name')->ignore($id)->whereNull('deleted_at'),
            ],
            'leave_type' => 'required|in:cutting,non-cutting',
            'min_service_length' => 'nullable|string',
            'max_leave_days' => 'required|integer|min:0',
        ];

        $request->validate($validator);

        DB::transaction(function () use ($request, $id) {
            $input = [
                'name' => $request->name,
                'leave_type' => $request->leave_type,
                'min_service_length' => $request->min_service_length,
                'max_leave_days' => $request->max_leave_days,
            ];

            $input['code'] = $this->generateLeaveOffCode($request->name, $id);

            LeaveOffSetting::updateOrCreate([
                'id' => $id,
            ], $input);
        });

        return redirect(route('leave-off-setting.index'))->with('success', 'Pengaturan cuti berhasil disimpan');
    }

    /**
     * Generate unique leave off code from name
     */
    private function generateLeaveOffCode($name, $excludeId = null)
    {
        // Create base code from name
        $baseCode = strtoupper(preg_replace('/[^A-Za-z0-9]/', '', $name));
        $baseCode = substr($baseCode, 0, 8); // Limit to 8 characters

        // If base code is empty, use default
        if (empty($baseCode)) {
            $baseCode = 'LEAVE';
        }

        $code = $baseCode;
        $counter = 1;

        // Check for uniqueness and add counter if needed
        while (LeaveOffSetting::where('code', $code)
                             ->when($excludeId, function($query) use ($excludeId) {
                                 return $query->where('id', '!=', $excludeId);
                             })
                             ->whereNull('deleted_at')
                             ->exists()) {
            $code = $baseCode . str_pad($counter, 2, '0', \STR_PAD_LEFT);
            $counter++;
        }

        return $code;
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        $data = LeaveOffSetting::findOrFail($id);
        return view('backoffice.leave-off-setting.show', compact('data'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        $data = LeaveOffSetting::findOrFail($id);
        $leaveTypes = LeaveOffSetting::getLeaveTypes();
        return view('backoffice.leave-off-setting.create-update', compact('data', 'leaveTypes'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        try {
            $leaveOffSetting = LeaveOffSetting::findOrFail($id);

            DB::transaction(function() use ($leaveOffSetting) {
                $leaveOffSetting->delete();
            });

            return response()->json(['status' => true,'message' => 'Data berhasil dihapus'], 200);

        } catch (\Throwable $th) {
           return response()->json(['status' => false,'message' => $th->getMessage()], 500);
        }
    }

    public function dataTable(Request $request)
    {
        $data = LeaveOffSetting::select(
            'id',
            'name',
            'code',
            'leave_type',
            'max_leave_days',
            'created_at'
        )
        ->latest()
        ->filter($request);

        return DataTables::of($data)
            ->addIndexColumn()
            ->addColumn('leave_type_badge', function ($data) {
                $badgeClass = $data->leave_type === 'cutting' ? 'bg-danger' : 'bg-success';
                return "<span class='badge {$badgeClass} px-3 py-2 rounded-full'>{$data->leave_type_name}</span>";
            })
            ->addColumn('max_leave_days_formatted', function ($data) {
                return $data->max_leave_days . ' hari';
            })
            ->addColumn('action', function ($data) {
                $action_button = '';
                
                if(canPermission('Master Pengaturan Cuti.Edit')) {
                    $action_button .= "<li>
                                            <a class='dropdown-item' href='".route('leave-off-setting.edit', $data->id)."'>
                                                <i class='feather feather-edit-3 me-3'></i>
                                                <span>Edit</span>
                                            </a>
                                        </li>";
                }

                if(canPermission('Master Pengaturan Cuti.Delete')) {
                    $action_button .= " <li class='dropdown-divider'></li>
                                        <li>
                                            <a class='dropdown-item deleteData' href='javascript:void(0)'  data-id='$data->id' data-input='".json_encode($data)."'>
                                                <i class='feather feather-trash-2 me-3'></i>
                                                <span>Delete</span>
                                            </a>
                                        </li>";
                }

                $action = " <div class='hstack gap-2'>
                                                <div class='dropdown dropdown-overflow'>
                                                    <a href='javascript:void(0)'
                                                        class='avatar-text avatar-md btn-dropdown'
                                                        data-bs-toggle='dropdown' data-bs-offset='0,21'>
                                                        <i class='feather feather-more-horizontal'></i>
                                                    </a>
                                                    <ul class='dropdown-menu'>
                                                        $action_button
                                                    </ul>
                                                </div>
                                            </div>";
                return $action;
            })
            ->rawColumns(['action', 'leave_type_badge'])
            ->smart(true)
            ->make(true);
    }

    /**
     * Get list of leave off settings for select options
     */
    public function list(Request $request)
    {
        $settings = LeaveOffSetting::select('id', 'name', 'code', 'leave_type', 'max_leave_days')
                                  ->when($request->keyword, function($q) use ($request) {
                                      $q->where('name', 'like', '%'.$request->keyword.'%')
                                        ->orWhere('code', 'like', '%'.$request->keyword.'%');
                                  })
                                  ->when($request->leave_type, function($q) use ($request) {
                                      $q->where('leave_type', $request->leave_type);
                                  })
                                  ->orderBy('name')
                                  ->get();
        
        return response()->json($settings);
    }
}
