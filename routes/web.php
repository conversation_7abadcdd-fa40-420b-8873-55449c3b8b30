<?php

use Illuminate\Support\Facades\Route;
use Intervention\Image\Facades\Image;
use Illuminate\Support\Facades\Request;
use App\Http\Controllers\BranchController;
use App\Http\Controllers\EsignerController;
use App\Http\Controllers\PenggunaController;
use App\Http\Controllers\SuratMasukController;
use App\Http\Controllers\UnitBisnisController;
use App\Http\Controllers\FileManagerController;
use App\Http\Controllers\NotificationController;
use App\Http\Controllers\AttendanceLogController;
use App\Http\Controllers\ProfileCustomController;
use App\Http\Controllers\DefaultSettingController;
use App\Http\Controllers\RolePermissionController;
use App\Http\Controllers\LeaveOffSettingController;
use App\Http\Controllers\UploadFileEsignController;
use App\Http\Controllers\UserAccessBranchController;
use App\Http\Controllers\AttendanceRequestController;
use App\Http\Controllers\WorkingHourSettingController;
use App\Http\Controllers\AttendanceTypeSettingController;
use App\Http\Controllers\WorkingCalendarSettingController;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "web" middleware group. Make something great!
|
*/

Route::get('/', function () {
    return redirect(route('login'));
});

Route::get('/dashboard', function () {
    return view('dashboard');
})->middleware(['auth', 'verified'])->name('dashboard');

Route::get('email-content', function () {
    return view('backoffice.email.surat-notification');
});

// Route::get('surat-masuk/render-pdf/{id}', [SuratMasukController::class, 'renderPdf'])->name('renderPdf');

Route::group(['middleware' => ['auth']], function () {
    Route::group(['controller' => ProfileCustomController::class], function () {
        Route::get('/profile', 'edit')->name('profile.edit');
        Route::patch('/profile', 'update')->name('profile.update');
    });

    Route::group(['controller' => PenggunaController::class, 'prefix' => 'pengguna', 'as' => 'pengguna.'], function () {
        Route::get('/', 'index')->name('index');
        Route::get('/create', 'create')->name('create');
        Route::post('/', 'store')->name('store');
        Route::get('/{id}/edit', 'edit')->name('edit');
        Route::put('/{id}', 'update')->name('update');
        Route::delete('/{id}', 'destroy')->name('destroy');

        Route::post('/dataTable', 'dataTable')->name('dataTable');

        Route::get('/list-group', 'listGroup')->name('list');
    });

    Route::group(['controller' => UnitBisnisController::class, 'prefix' => 'unit-bisnis', 'as' => 'unit-bisnis.'], function () {
        Route::get('/', 'index')->name('index');
        Route::get('/create', 'create')->name('create');
        Route::post('/', 'store')->name('store');
        Route::get('/{id}', 'show')->name('show');
        Route::get('/{id}/edit', 'edit')->name('edit');
        Route::put('/{id}', 'update')->name('update');
        Route::delete('/{id}', 'destroy')->name('destroy');

        Route::post('/dataTable', 'dataTable')->name('dataTable');

        Route::post('change-session', 'changeSession')->name('changeSession');
    });

    // Master Data Routes
    Route::group(['controller' => BranchController::class, 'prefix' => 'branch', 'as' => 'branch.'], function () {
        Route::get('/', 'index')->name('index');
        Route::get('/create', 'create')->name('create');
        Route::post('/', 'store')->name('store');
        Route::get('/{id}', 'show')->name('show');
        Route::get('/{id}/edit', 'edit')->name('edit');
        Route::put('/{id}', 'update')->name('update');
        Route::delete('/{id}', 'destroy')->name('destroy');

        Route::post('/dataTable', 'dataTable')->name('dataTable');
        Route::get('/list', 'list')->name('list');
    });

    Route::group(['controller' => LeaveOffSettingController::class, 'prefix' => 'leave-off-setting', 'as' => 'leave-off-setting.'], function () {
        Route::get('/', 'index')->name('index');
        Route::get('/create', 'create')->name('create');
        Route::post('/', 'store')->name('store');
        Route::get('/{id}', 'show')->name('show');
        Route::get('/{id}/edit', 'edit')->name('edit');
        Route::put('/{id}', 'update')->name('update');
        Route::delete('/{id}', 'destroy')->name('destroy');

        Route::post('/dataTable', 'dataTable')->name('dataTable');
        Route::get('/list', 'list')->name('list');
    });

    Route::group(['controller' => WorkingHourSettingController::class, 'prefix' => 'working-hour-setting', 'as' => 'working-hour-setting.'], function () {
        Route::get('/', 'index')->name('index');
        Route::get('/create', 'create')->name('create');
        Route::post('/', 'store')->name('store');
        Route::get('/{id}', 'show')->name('show');
        Route::get('/{id}/edit', 'edit')->name('edit');
        Route::put('/{id}', 'update')->name('update');
        Route::delete('/{id}', 'destroy')->name('destroy');

        Route::post('/dataTable', 'dataTable')->name('dataTable');
        Route::get('/list', 'list')->name('list');
    });

    Route::group(['controller' => AttendanceTypeSettingController::class, 'prefix' => 'attendance-type-setting', 'as' => 'attendance-type-setting.'], function () {
        Route::get('/', 'index')->name('index');
        Route::get('/create', 'create')->name('create');
        Route::post('/', 'store')->name('store');
        Route::get('/{id}', 'show')->name('show');
        Route::get('/{id}/edit', 'edit')->name('edit');
        Route::put('/{id}', 'update')->name('update');
        Route::delete('/{id}', 'destroy')->name('destroy');

        Route::post('/dataTable', 'dataTable')->name('dataTable');
        Route::get('/list', 'list')->name('list');
    });

    Route::group(['controller' => WorkingCalendarSettingController::class, 'prefix' => 'working-calendar-setting', 'as' => 'working-calendar-setting.'], function () {
        Route::get('/', 'index')->name('index');
        Route::get('/create', 'create')->name('create');
        Route::post('/', 'store')->name('store');

        // Special routes must come before parameterized routes
        Route::post('/dataTable', 'dataTable')->name('dataTable');
        Route::post('/generate-calendar', 'generateCalendar')->name('generateCalendar');
        Route::get('/calendar-data', 'getCalendarData')->name('getCalendarData');
        Route::post('/upload-excel', 'uploadExcel')->name('uploadExcel');
        Route::get('/download-template', 'downloadTemplate')->name('downloadTemplate');
        Route::post('/bulk-update', 'bulkUpdate')->name('bulk-update');

        // Parameterized routes come last
        Route::get('/{id}', 'show')->name('show');
        Route::get('/{id}/edit', 'edit')->name('edit');
        Route::put('/{id}', 'update')->name('update');
        Route::delete('/{id}', 'destroy')->name('destroy');
    });

    Route::group(['controller' => UserAccessBranchController::class, 'prefix' => 'user-access-branch', 'as' => 'user-access-branch.'], function () {
        Route::get('/', 'index')->name('index');
        Route::post('/store', 'store')->name('store');
        Route::get('/get-user-access', 'getUserAccess')->name('getUserAccess');
        Route::post('/remove-access', 'removeAccess')->name('removeAccess');
        Route::post('/dataTable', 'dataTable')->name('dataTable');
        Route::get('/user-branches', 'getUserBranches')->name('getUserBranches');
        Route::get('/check-access', 'checkAccess')->name('checkAccess');
    });

    Route::group(['controller' => AttendanceLogController::class, 'prefix' => 'attendance-log', 'as' => 'attendance-log.'], function () {
        Route::get('/', 'index')->name('index');
        Route::post('/dataTable', 'dataTable')->name('dataTable');
        Route::post('/upload-excel', 'uploadExcel')->name('uploadExcel');
        Route::get('/download-template', 'downloadTemplate')->name('downloadTemplate');
        Route::delete('/{id}', 'destroy')->name('destroy');
        Route::post('/bulk-delete', 'bulkDelete')->name('bulkDelete');
        Route::post('/delete-all', 'deleteAll')->name('deleteAll');
    });

    Route::group(['controller' => AttendanceRequestController::class, 'prefix' => 'attendance-request', 'as' => 'attendance-request.'], function () {
        Route::get('/', 'index')->name('index');
        Route::get('/create', 'create')->name('create');
        Route::post('/', 'store')->name('store');
        Route::get('/{id}', 'show')->name('show');
        Route::get('/{id}/edit', 'edit')->name('edit');
        Route::put('/{id}', 'update')->name('update');
        Route::delete('/{id}', 'destroy')->name('destroy');
        Route::post('/dataTable', 'dataTable')->name('dataTable');
        Route::post('/{id}/approve', 'approve')->name('approve');
        Route::post('/{id}/reject', 'reject')->name('reject');
        Route::get('/employees/by-branch', 'getEmployeesByBranch')->name('getEmployeesByBranch');
        Route::get('/export/excel', 'exportExcel')->name('exportExcel');
        Route::post('/bulk-delete', 'bulkDelete')->name('bulkDelete');
    });

    Route::group(['controller' => FileManagerController::class, 'prefix' => 'file-manager', 'as' => 'file-manager.'], function () {
        Route::post('upload', 'uploadFile')->name('upload');
    });

    Route::group([], function () {
        Route::resource('role-permission', RolePermissionController::class);
        Route::post('role-permission/dataTable', [RolePermissionController::class, 'dataTable'])->name('role-permission.dataTable');
        Route::post('role-permission/dataTable/permission', [RolePermissionController::class, 'dataTablePermission'])->name('role-permission.dataTablePermission');
        Route::post('role-permission/store-permission', [RolePermissionController::class, 'storePermission'])->name('role-permission.store-permission')->middleware('can:Role & Permission.Create');
        Route::delete('role-permission/{id}/permission', [RolePermissionController::class, 'destroyPermission'])->name('role-permission.destroy-permission')->middleware('can:Role & Permission.Delete');
    });

    Route::group(['prefix' => 'pengaturan', 'as' => 'pengaturan.'], function () {
        Route::group(['prefix' => 'default', 'as' => 'default.', 'controller' => DefaultSettingController::class], function () {
            Route::get('/', 'index')->name('index');
            Route::post('/', 'store')->name('store');
        });
        Route::group(['prefix' => 'e-sign', 'as' => 'e-sign.', 'controller' => EsignerController::class], function () {
            Route::get('/', 'index')->name('index');
            Route::post('/', 'store')->name('store');
            Route::post('/try-connect-esign', 'tryConnectEsign')->name('try-connect-esign');
        });
    });


    Route::group(['controller' => NotificationController::class, 'prefix' => 'notification', 'as' => 'notification.'], function () {
        Route::get('/list', 'list')->name('list');
        Route::get('/count-no-read', 'countNoRead')->name('countNoRead');
        Route::post('make-as-read', 'makeAsRead')->name('makeAsRead');
        Route::get('/', 'index')->name('index');
    });
});

require __DIR__ . '/auth.php';

Route::get('/test-db', function () {
    try {
        DB::connection()->getPdo();
        return "Database connection is successful.";
    } catch (\Exception $e) {
        return "Could not connect to the database. Please check your configuration. Error: " . $e->getMessage();
    }
});
