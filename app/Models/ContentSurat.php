<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class ContentSurat extends Model
{
    use HasFactory, HasUuids;
    protected $guarded = ['id'];
    protected $appends = ['content_replace', 'content_replace_pdf'];
    
    public function getContentReplaceAttribute()
    {
        // dd(parseContent($this->content ?? '', $this->surat_id));
       return parseContent($this->content ?? '', $this->surat_id) ?? '';
    }

    public function getContentReplacePdfAttribute()
    {
        // dd(parseContent($this->content ?? '', $this->surat_id));
       return parseContent($this->content ?? '', $this->surat_id, [
        'is_pdf' => true
       ]) ?? '';
    }

    public function surat()
    {
        return $this->belongsTo(Surat::class, 'surat_id');
    }
}
