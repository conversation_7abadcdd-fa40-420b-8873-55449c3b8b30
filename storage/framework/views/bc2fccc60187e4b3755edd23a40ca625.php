<?php if (isset($component)) { $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54 = $attributes; } ?>
<?php $component = App\View\Components\AppLayout::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('app-layout'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(App\View\Components\AppLayout::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
     <?php $__env->slot('header', null, []); ?> 
        <?php echo e($data ?? null ? 'Edit' : 'Tambah'); ?> Pengajuan Absen
     <?php $__env->endSlot(); ?>
     <?php $__env->slot('headerRight', null, []); ?> 
        <div class="page-header-right-items">
            <div class="d-flex align-items-center gap-2 page-header-right-items-wrapper">
                <a href="<?php echo e(route('attendance-request.index')); ?>" class="btn btn-light-brand">
                    <i class="feather-x me-2"></i>
                    <span>Batal</span>
                </a>
                <button class="btn btn-primary btnSubmit" style="width: 150px">
                    <i class="feather-save me-2"></i>
                    <span>Simpan</span>
                </button>
            </div>
        </div>
        <div class="d-md-none d-flex align-items-center">
            <a href="javascript:void(0)" class="page-header-right-open-toggle">
                <i class="feather-align-right fs-20"></i>
            </a>
        </div>
     <?php $__env->endSlot(); ?>
    
    <div class="row">
        <div class="col-12">
            <div class="card stretch stretch-full">
                <div class="card-body">
                    <?php if (isset($component)) { $__componentOriginalb24df6adf99a77ed35057e476f61e153 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalb24df6adf99a77ed35057e476f61e153 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.validation-errors','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('validation-errors'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalb24df6adf99a77ed35057e476f61e153)): ?>
<?php $attributes = $__attributesOriginalb24df6adf99a77ed35057e476f61e153; ?>
<?php unset($__attributesOriginalb24df6adf99a77ed35057e476f61e153); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalb24df6adf99a77ed35057e476f61e153)): ?>
<?php $component = $__componentOriginalb24df6adf99a77ed35057e476f61e153; ?>
<?php unset($__componentOriginalb24df6adf99a77ed35057e476f61e153); ?>
<?php endif; ?>
                    <?php if(session('error')): ?>
                        <div class="alert alert-danger">
                            <?php echo e(session('error')); ?>

                        </div>
                    <?php endif; ?>
                    
                    <form class="needs-validation" novalidate 
                          action="<?php echo e(isset($data) ? route('attendance-request.update', $data->id) : route('attendance-request.store')); ?>" 
                          id="formSubmit" method="POST" enctype="multipart/form-data">
                        <?php echo csrf_field(); ?>
                        <?php if(isset($data)): ?>
                            <?php echo method_field('PUT'); ?>
                        <?php endif; ?>
                        
                        <div class="row g-4">
                            <div class="col-md-6 mb-4">
                                <label class="form-label">Cabang <span class="text-danger">*</span></label>
                                <?php
                                    $branches = App\Models\Branch::active()->pluck('name', 'id');
                                ?>
                                <select class="form-control select2" name="branch_id" id="branch_id" required>
                                    <option value="">Pilih Cabang</option>
                                    <?php $__currentLoopData = $branches; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $value): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <option value="<?php echo e($key); ?>" <?php echo e(old('branch_id', ($data->branch_id ?? null)) == $key ? 'selected' : ''); ?>>
                                            <?php echo e($value); ?>

                                        </option>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </select>
                                <?php $__errorArgs = ['branch_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <div class="invalid-feedback d-block"><?php echo e($message); ?></div>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>
                            
                            <div class="col-md-6 mb-4">
                                <label class="form-label">Nama Karyawan <span class="text-danger">*</span></label>
                                <select class="form-control select2" name="employee_user_id" id="employee_user_id" required disabled>
                                    <option value="">Pilih cabang terlebih dahulu</option>
                                </select>
                                <?php $__errorArgs = ['employee_user_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <div class="invalid-feedback d-block"><?php echo e($message); ?></div>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>
                        </div>
                        
                        <div class="row g-4">
                            <div class="col-md-6 mb-4">
                                <label class="form-label">Tanggal Absen <span class="text-danger">*</span></label>
                                <input type="date" class="form-control" name="attendance_date" 
                                       value="<?php echo e(old('attendance_date', isset($data) ? $data->attendance_date->format('Y-m-d') : '')); ?>" 
                                       min="<?php echo e(date('Y-m-d')); ?>" required>
                                <?php $__errorArgs = ['attendance_date'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <div class="invalid-feedback d-block"><?php echo e($message); ?></div>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>
                            
                            <div class="col-md-6 mb-4">
                                <label class="form-label">Jenis Absen <span class="text-danger">*</span></label>
                                <?php
                                    $attendanceTypes = App\Models\AttendanceTypeSetting::pluck('name', 'id');
                                ?>
                                <select class="form-control select2" name="attendance_type_id" required>
                                    <option value="">Pilih Jenis Absen</option>
                                    <?php $__currentLoopData = $attendanceTypes; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $value): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <option value="<?php echo e($key); ?>" <?php echo e(old('attendance_type_id', ($data->attendance_type_id ?? null)) == $key ? 'selected' : ''); ?>>
                                            <?php echo e($value); ?>

                                        </option>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </select>
                                <?php $__errorArgs = ['attendance_type_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <div class="invalid-feedback d-block"><?php echo e($message); ?></div>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>
                        </div>
                        
                        <div class="row g-4">
                            <div class="col-md-12">
                                <label class="form-label">Keterangan/Alasan <span class="text-danger">*</span></label>
                                <textarea class="form-control" name="reason" rows="4" 
                                          placeholder="Masukkan keterangan atau alasan pengajuan absen..." required><?php echo e(old('reason', ($data->reason ?? null))); ?></textarea>
                                <?php $__errorArgs = ['reason'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <div class="invalid-feedback d-block"><?php echo e($message); ?></div>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>
                        </div>
                        
                        <div class="row mt-4">
                            <div class="col-md-12">
                                <div class="card border-info">
                                    <div class="card-header bg-info text-white">
                                        <h6 class="mb-0 text-white">
                                            <i class="feather-paperclip me-2"></i>
                                            Lampiran (Opsional)
                                        </h6>
                                    </div>
                                    <div class="card-body">
                                        <?php if(isset($data) && $data->attachment): ?>
                                            <div class="mb-3">
                                                <div class="alert alert-info d-flex align-items-center">
                                                    <i class="feather-file me-2"></i>
                                                    <span>File saat ini: </span>
                                                    <a href="<?php echo e(asset($data->attachment)); ?>" target="_blank" class="ms-2 fw-bold">
                                                        <?php echo e(basename($data->attachment)); ?>

                                                    </a>
                                                </div>
                                            </div>
                                        <?php endif; ?>
                                        
                                        <input type="file" class="form-control" name="attachment" id="attachment"
                                               accept=".jpg,.jpeg,.png,.pdf,.doc,.docx">
                                        <small class="form-text text-muted mt-2 d-block">
                                            <i class="feather-info me-1"></i>
                                            Format yang diizinkan: JPG, JPEG, PNG, PDF, DOC, DOCX. Maksimal 2MB.
                                            <?php if(isset($data) && $data->attachment): ?>
                                                <br><span class="text-warning">Biarkan kosong jika tidak ingin mengubah lampiran.</span>
                                            <?php endif; ?>
                                        </small>
                                        <?php $__errorArgs = ['attachment'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                            <div class="invalid-feedback d-block mt-2"><?php echo e($message); ?></div>
                                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <?php if(isset($data)): ?>
                            <div class="row g-4">
                                <div class="col-md-12">
                                    <div class="card border-warning">
                                        <div class="card-header bg-warning text-dark">
                                            <h6 class="mb-0">
                                                <i class="feather-info me-2"></i>
                                                Informasi Pengajuan
                                            </h6>
                                        </div>
                                        <div class="card-body">
                                            <div class="row">
                                                <div class="col-md-3">
                                                    <strong>Status:</strong><br>
                                                    <?php echo $data->status_badge; ?>

                                                </div>
                                                <div class="col-md-3">
                                                    <strong>Diajukan Oleh:</strong><br>
                                                    <?php echo e($data->user->nama ?? '-'); ?>

                                                </div>
                                                <div class="col-md-3">
                                                    <strong>Tanggal Pengajuan:</strong><br>
                                                    <?php echo e($data->created_at_formatted); ?>

                                                </div>
                                                <div class="col-md-3">
                                                    <?php if($data->approved_by): ?>
                                                        <strong>Diproses Oleh:</strong><br>
                                                        <?php echo e($data->approvedBy->nama ?? '-'); ?><br>
                                                        <small class="text-muted"><?php echo e($data->approved_at_formatted); ?></small>
                                                    <?php else: ?>
                                                        <strong>Status:</strong><br>
                                                        <span class="text-warning">Menunggu Persetujuan</span>
                                                    <?php endif; ?>
                                                </div>
                                            </div>
                                            
                                            <?php if($data->rejection_reason): ?>
                                                <div class="mt-3">
                                                    <strong class="text-danger">Alasan Penolakan:</strong><br>
                                                    <div class="text-danger"><?php echo e($data->rejection_reason); ?></div>
                                                </div>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        <?php endif; ?>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <?php $__env->startPush('scripts'); ?>
    <script>
    $(document).ready(function() {
        // Initialize Select2
        $('.select2').select2({
            theme: 'bootstrap-5',
            width: '100%'
        });

        // Load employees when page loads (for edit mode)
        <?php if(isset($data)): ?>
            loadEmployees('<?php echo e($data->branch_id); ?>', '<?php echo e($data->employee_user_id); ?>');
        <?php endif; ?>
        
        // Load employees when branch is selected
        $('#branch_id').change(function() {
            let branchId = $(this).val();
            loadEmployees(branchId);
        });

        function loadEmployees(branchId, selectedEmployeeId = null) {
            let employeeSelect = $('#employee_user_id');
            
            if (branchId) {
                employeeSelect.prop('disabled', true).html('<option value="">Memuat...</option>');
                
                $.ajax({
                    url: "<?php echo e(route('attendance-request.getEmployeesByBranch')); ?>",
                    type: 'GET',
                    data: { branch_id: branchId },
                    success: function(response) {
                        employeeSelect.html('<option value="">Pilih Karyawan</option>');
                        
                        if (response.length > 0) {
                            $.each(response, function(index, employee) {
                                let selected = selectedEmployeeId && employee.id == selectedEmployeeId ? 'selected' : '';
                                employeeSelect.append(`<option value="${employee.id}" ${selected}>${employee.nama} (${employee.email})</option>`);
                            });
                            employeeSelect.prop('disabled', false);
                        } else {
                            employeeSelect.html('<option value="">Tidak ada karyawan aktif di cabang ini</option>');
                        }
                        
                        // Reinitialize Select2
                        employeeSelect.select2({
                            theme: 'bootstrap-5',
                            width: '100%'
                        });
                    },
                    error: function(xhr) {
                        employeeSelect.html('<option value="">Error memuat data karyawan</option>');
                        if (xhr.status === 403) {
                            Swal.fire('Error!', 'Anda tidak memiliki akses ke cabang ini', 'error');
                        } else {
                            Swal.fire('Error!', 'Terjadi kesalahan saat memuat data karyawan', 'error');
                        }
                    }
                });
            } else {
                employeeSelect.prop('disabled', true).html('<option value="">Pilih cabang terlebih dahulu</option>');
                employeeSelect.select2({
                    theme: 'bootstrap-5',
                    width: '100%'
                });
            }
        }

        // File upload preview
        $('#attachment').change(function() {
            let file = this.files[0];
            if (file) {
                let fileSize = file.size / 1024 / 1024; // Convert to MB
                let fileName = file.name;
                
                if (fileSize > 2) {
                    Swal.fire('Error!', 'Ukuran file tidak boleh lebih dari 2MB', 'error');
                    $(this).val('');
                    return;
                }
                
                // Show file info
                let fileInfo = `File dipilih: ${fileName} (${fileSize.toFixed(2)} MB)`;
                $(this).next('.form-text').html('<i class="feather-info me-1"></i>' + fileInfo);
            }
        });

        // Form submission
        $('.btnSubmit').click(function(e) {
            e.preventDefault();
            
            let form = $('#formSubmit')[0];
            if (form.checkValidity()) {
                // Show loading
                $(this).prop('disabled', true).html('<i class="feather-loader me-2"></i>Menyimpan...');
                form.submit();
            } else {
                form.classList.add('was-validated');
            }
        });
    });
    </script>
    <?php $__env->stopPush(); ?>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $attributes = $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $component = $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php /**PATH /Users/<USER>/ProjectWork/hris-system/resources/views/backoffice/attendance-request/create-update.blade.php ENDPATH**/ ?>