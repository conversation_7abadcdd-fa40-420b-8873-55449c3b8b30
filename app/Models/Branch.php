<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Branch extends Model
{
    use HasFactory, HasUuids, SoftDeletes;

    protected $guarded = ['id'];

    protected $casts = [
        'status' => 'string',
    ];

    public function userAccessBranches()
    {
        return $this->hasMany(UserAccessBranch::class);
    }

    public function users()
    {
        return $this->belongsToMany(User::class, 'user_access_branches');
    }

    public function leaveBranchSettings()
    {
        return $this->hasMany(LeaveBranchSetting::class);
    }

    public function scopeFilter($query, $request)
    {
        return $query->when($request->keyword, function ($q, $filter) {
            $q->where('name', 'like', '%' . $filter . '%')
                ->orWhere('code', 'like', '%' . $filter . '%');
        })->when($request->status, function ($q, $status) {
            $q->where('status', $status);
        });
    }

    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }
}
