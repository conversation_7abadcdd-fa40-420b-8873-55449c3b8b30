<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Employee extends Model
{
    use HasFactory, HasUuids, SoftDeletes;

    protected $guarded = ['id'];

    protected $casts = [
        'hari_off' => 'array',
        'join_date' => 'date',
        'tanggal_lahir' => 'date',
        'resign_date' => 'date',
        'tanggal_deposit' => 'date',
        'tanggal_pengembalian_deposit' => 'date',
        'deposit_seragam' => 'decimal:2',
        'pengembalian_seragam' => 'decimal:2',
        'jumlah_cuti_tahunan' => 'integer',
        'masa_kerja_hari' => 'integer',
        'jumlah_seragam' => 'integer',
    ];

    protected $appends = ['avatar_url'];

    public function getAvatarUrlAttribute()
    {
        if ($this->profile_photo) {
            return $this->profile_photo;
        } else {
            $nama = urlencode($this->nama);
            return "https://ui-avatars.com/api/?name={$nama}&color=7F9CF5&background=EBF4FF";
        }
    }

    // Relations
    public function role()
    {
        return $this->belongsTo(Role::class);
    }

    public function branch()
    {
        return $this->belongsTo(Branch::class);
    }

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    public function scopeInactive($query)
    {
        return $query->where('status', 'inactive');
    }

    public function scopeResigned($query)
    {
        return $query->where('status', 'resigned');
    }

    public function scopeFilter($query, $filters)
    {
        return $query->when($filters['keyword'] ?? null, function ($query, $search) {
            $query->where(function ($query) use ($search) {
                $query->where('nama', 'like', '%'.$search.'%')
                    ->orWhere('kode', 'like', '%'.$search.'%')
                    ->orWhere('nama_asli_ktp', 'like', '%'.$search.'%')
                    ->orWhereHas('role', function ($query) use ($search) {
                        $query->where('name', 'like', '%'.$search.'%');
                    })
                    ->orWhereHas('branch', function ($query) use ($search) {
                        $query->where('name', 'like', '%'.$search.'%');
                    });
            });
        })->when($filters['status'] ?? null, function ($query, $status) {
            $query->where('status', $status);
        })->when($filters['branch_id'] ?? null, function ($query, $branchId) {
            $query->where('branch_id', $branchId);
        })->when($filters['role_id'] ?? null, function ($query, $roleId) {
            $query->where('role_id', $roleId);
        });
    }

    // Accessors
    public function getFormattedJoinDateAttribute()
    {
        return $this->join_date ? $this->join_date->format('d/m/Y') : '-';
    }

    public function getFormattedTanggalLahirAttribute()
    {
        return $this->tanggal_lahir ? $this->tanggal_lahir->format('d/m/Y') : '-';
    }

    public function getFormattedResignDateAttribute()
    {
        return $this->resign_date ? $this->resign_date->format('d/m/Y') : '-';
    }

    public function getStatusBadgeAttribute()
    {
        $badges = [
            'active' => '<span class="badge bg-success">Aktif</span>',
            'inactive' => '<span class="badge bg-warning">Tidak Aktif</span>',
            'resigned' => '<span class="badge bg-danger">Resign</span>',
        ];

        return $badges[$this->status] ?? '<span class="badge bg-secondary">Unknown</span>';
    }

    public function getGenderLabelAttribute()
    {
        return $this->gender === 'male' ? 'Laki-laki' : ($this->gender === 'female' ? 'Perempuan' : '-');
    }

    public function getHariOffFormattedAttribute()
    {
        if (!$this->hari_off || !is_array($this->hari_off)) {
            return '-';
        }

        $days = [
            'monday' => 'Senin',
            'tuesday' => 'Selasa', 
            'wednesday' => 'Rabu',
            'thursday' => 'Kamis',
            'friday' => 'Jumat',
            'saturday' => 'Sabtu',
            'sunday' => 'Minggu'
        ];

        $formatted = [];
        foreach ($this->hari_off as $day) {
            if (isset($days[$day])) {
                $formatted[] = $days[$day];
            }
        }

        return implode(', ', $formatted);
    }
}
