<?php

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class HistorySurat extends Model
{
    use HasFactory, HasUuids;
    protected $guarded = ['id'];

    protected $appends = ['created_at_formatted', 'approve_status_name_formatted', 'diffor_humans', 'approve_status_name'];

    public function getApproveStatusNameFormattedAttribute()
    {
        $value = array_search($this->approval_status, Surat::APPROVE_STATUS);
        $approve_format =  $this->approval_status ? ucwords(str_replace('_', ' ', $value)) : null;

        return approve_status($this->approval_status, $approve_format);
    }

    public function getApproveStatusNameAttribute()
    {
        $value = array_search($this->approval_status, Surat::APPROVE_STATUS);
        return $value;
    }

    public function getCreatedAtFormattedAttribute()
    {
        return $this->created_at ? Carbon::parse($this->created_at)->translatedFormat('d F Y H:i') : $this->created_at;
    }

    public function getDifforHumansAttribute()
    {
        return $this->created_at ? $this->created_at->diffForHumans() : $this->created_at;
    }

    public function surat()
    {
        return $this->belongsTo(Surat::class);
    }

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function role()
    {
        return $this->belongsTo(Role::class);
    }

    public function readtNotifications()
    {
        return $this->hasOne(ReadNotification::class, 'history_surat_id');
    }
}
