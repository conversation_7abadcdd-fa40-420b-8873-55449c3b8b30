<?php

namespace App\Providers;

use Carbon\Carbon;
use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\View;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Blade;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {

        // if(empty(Session()->get('tahun_akademik_id')) && empty(Session()->get('tahun_akademik'))) {
        //     $tahun_akademik = TahunAkademik::latest()->first();
        //     if($tahun_akademik) {
        //         Session()->put('tahun_akademik_id', $tahun_akademik->id);
        //         Session()->put('tahun_akademik', $tahun_akademik->tahun_akademik);
        //     }
        // }

        // config(['app.locale' => 'id']);
	    // Carbon::setLocale('id');

        // $tahun_akademik = TahunAkademik::orderBy('tahun_akademik', 'DESC')
        //                                 ->get();
        // View::share('tahun_akademik', $tahun_akademik);

        view()->composer('*', function ($view) {
            $view->with('Carbon', new Carbon);
        });

        Carbon::setLocale('id');
        setlocale(LC_TIME, 'id_ID.UTF-8'); // Untuk format strftime
        App::setLocale('id');
    }
}
