<?php if (isset($component)) { $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54 = $attributes; } ?>
<?php $component = App\View\Components\AppLayout::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('app-layout'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(App\View\Components\AppLayout::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
     <?php $__env->slot('header', null, []); ?> 
        <?php echo e(isset($data) ? 'Edit' : 'Tambah'); ?> Data Karyawan
     <?php $__env->endSlot(); ?>
     <?php $__env->slot('headerRight', null, []); ?> 
        <div class="d-flex align-items-center gap-2 page-header-right-items-wrapper">
            <a href="<?php echo e(route('employee.index')); ?>" class="btn btn-outline-primary">
                <i class="feather-arrow-left me-2"></i>
                <span>Kembali</span>
            </a>
        </div>
     <?php $__env->endSlot(); ?>

    <div class="row">
        <div class="col-lg-12">
            <div class="card stretch stretch-full">
                <div class="card-body">
                    <?php if (isset($component)) { $__componentOriginalb24df6adf99a77ed35057e476f61e153 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalb24df6adf99a77ed35057e476f61e153 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.validation-errors','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('validation-errors'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalb24df6adf99a77ed35057e476f61e153)): ?>
<?php $attributes = $__attributesOriginalb24df6adf99a77ed35057e476f61e153; ?>
<?php unset($__attributesOriginalb24df6adf99a77ed35057e476f61e153); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalb24df6adf99a77ed35057e476f61e153)): ?>
<?php $component = $__componentOriginalb24df6adf99a77ed35057e476f61e153; ?>
<?php unset($__componentOriginalb24df6adf99a77ed35057e476f61e153); ?>
<?php endif; ?>
                    <?php if(session('error')): ?>
                        <div class="alert alert-danger">
                            <?php echo e(session('error')); ?>

                        </div>
                    <?php endif; ?>
                    <form
                        class="needs-validation"
                        novalidate
                        action="<?php echo e(route('employee.store')); ?>"
                        id="formSubmit" method="POST">
                        <?php echo csrf_field(); ?>
                        <?php if($data ?? false): ?>
                            <input type="hidden" name="id" value="<?php echo e($data->id); ?>">
                        <?php endif; ?>
                        
                        <!-- Biodata Section -->
                        <div class="card mb-4">
                            <div class="card-header py-3 bg-dark">
                                <h5 class="mb-0 text-white fw-bold fs-16">Biodata</h5>
                            </div>
                            <div class="card-body">
                                <div class="row g-4">
                                    <div class="col-md-4">
                                        <label class="form-label">Nama <span class="text-danger">*</span></label>
                                        <input type="text" class="form-control" name="nama" placeholder="Nama" required value="<?php echo e(old('nama', ($data->nama ?? null))); ?>">
                                    </div>
                                    <div class="col-md-4">
                                        <label class="form-label">Role</label>
                                        <?php
                                            $roles = App\Models\Role::pluck('name', 'id');
                                        ?>
                                        <select class="form-control select2" name="role_id" data-select2-selector="icon">
                                            <option value="">Pilih Role</option>
                                            <?php $__currentLoopData = $roles; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $value): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                <option value="<?php echo e($key); ?>" <?php echo e(old('role_id', ($data->role_id ?? null)) == $key ? 'selected' : ''); ?>><?php echo e($value); ?></option>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                        </select>
                                    </div>
                                    <div class="col-md-4">
                                        <label class="form-label">Join Date</label>
                                        <input type="date" class="form-control" name="join_date" value="<?php echo e(old('join_date', (($data->join_date ?? null) ? $data->join_date->format('Y-m-d') : null))); ?>">
                                    </div>
                                    <div class="col-md-4">
                                        <label class="form-label">Kode</label>
                                        <input type="text" class="form-control" name="kode" placeholder="Kode Karyawan" value="<?php echo e(old('kode', ($data->kode ?? null))); ?>">
                                    </div>
                                    <div class="col-md-4">
                                        <label class="form-label">Source</label>
                                        <input type="text" class="form-control" name="source" placeholder="Source" value="<?php echo e(old('source', ($data->source ?? null))); ?>">
                                    </div>
                                    <div class="col-md-4">
                                        <label class="form-label">Gender</label>
                                        <select class="form-control select2" name="gender">
                                            <option value="">Pilih Gender</option>
                                            <option value="male" <?php echo e(old('gender', ($data->gender ?? null)) == 'male' ? 'selected' : ''); ?>>Laki-laki</option>
                                            <option value="female" <?php echo e(old('gender', ($data->gender ?? null)) == 'female' ? 'selected' : ''); ?>>Perempuan</option>
                                        </select>
                                    </div>
                                    <div class="col-md-4">
                                        <label class="form-label">Tanggal Lahir</label>
                                        <input type="date" class="form-control" name="tanggal_lahir" value="<?php echo e(old('tanggal_lahir', (($data->tanggal_lahir ?? null) ? $data->tanggal_lahir->format('Y-m-d') : null))); ?>">
                                    </div>
                                    <div class="col-md-4">
                                        <label class="form-label">Nama Asli KTP</label>
                                        <input type="text" class="form-control" name="nama_asli_ktp" placeholder="Nama Asli KTP" value="<?php echo e(old('nama_asli_ktp', ($data->nama_asli_ktp ?? null))); ?>">
                                    </div>
                                    <div class="col-md-4">
                                        <label class="form-label">Hari Off (Optional)</label>
                                        <?php
                                            $days = [
                                                'monday' => 'Senin',
                                                'tuesday' => 'Selasa',
                                                'wednesday' => 'Rabu',
                                                'thursday' => 'Kamis',
                                                'friday' => 'Jumat',
                                                'saturday' => 'Sabtu',
                                                'sunday' => 'Minggu'
                                            ];
                                            $selected_days = old('hari_off', ($data->hari_off ?? []));
                                        ?>
                                        <select class="form-control select2" name="hari_off[]" multiple>
                                            <?php $__currentLoopData = $days; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $value): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                <option value="<?php echo e($key); ?>" <?php echo e(in_array($key, $selected_days) ? 'selected' : ''); ?>><?php echo e($value); ?></option>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                        </select>
                                    </div>
                                    <div class="col-md-4">
                                        <label class="form-label">Status <span class="text-danger">*</span></label>
                                        <select class="form-control select2" name="status" required>
                                            <option value="">Pilih Status</option>
                                            <option value="active" <?php echo e(old('status', ($data->status ?? 'active')) == 'active' ? 'selected' : ''); ?>>Aktif</option>
                                            <option value="inactive" <?php echo e(old('status', ($data->status ?? null)) == 'inactive' ? 'selected' : ''); ?>>Tidak Aktif</option>
                                            <option value="resigned" <?php echo e(old('status', ($data->status ?? null)) == 'resigned' ? 'selected' : ''); ?>>Resign</option>
                                        </select>
                                    </div>
                                    <div class="col-md-4">
                                        <label class="form-label">Resign Date</label>
                                        <input type="date" class="form-control" name="resign_date" value="<?php echo e(old('resign_date', (($data->resign_date ?? null) ? $data->resign_date->format('Y-m-d') : null))); ?>">
                                    </div>
                                    <div class="col-md-4">
                                        <label class="form-label">Cabang</label>
                                        <?php
                                            $branches = App\Models\Branch::active()->pluck('name', 'id');
                                        ?>
                                        <select class="form-control select2" name="branch_id">
                                            <option value="">Pilih Cabang</option>
                                            <?php $__currentLoopData = $branches; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $value): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                <option value="<?php echo e($key); ?>" <?php echo e(old('branch_id', ($data->branch_id ?? null)) == $key ? 'selected' : ''); ?>><?php echo e($value); ?></option>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Data Rekening Section -->
                        <div class="card mb-4">
                            <div class="card-header py-3" style="background-color: #4a90a4; ">
                                <h5 class="mb-0 fw-bold fs-16 text-white">Data Rekening</h5>
                            </div>
                            <div class="card-body">
                                <div class="row g-4">
                                    <div class="col-md-4">
                                        <label class="form-label">Nama Rekening</label>
                                        <input type="text" class="form-control" name="nama_rekening" placeholder="Nama Rekening" value="<?php echo e(old('nama_rekening', ($data->nama_rekening ?? null))); ?>">
                                    </div>
                                    <div class="col-md-4">
                                        <label class="form-label">No Rekening</label>
                                        <input type="text" class="form-control" name="no_rekening" placeholder="No Rekening" value="<?php echo e(old('no_rekening', ($data->no_rekening ?? null))); ?>">
                                    </div>
                                    <div class="col-md-4">
                                        <label class="form-label">Bank</label>
                                        <input type="text" class="form-control" name="bank" placeholder="Bank" value="<?php echo e(old('bank', ($data->bank ?? null))); ?>">
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Data Perlengkapan Section -->
                        <div class="card mb-4">
                            <div class="card-header py-3" style="background-color: #4a90a4;">
                                <h5 class="mb-0 fw-bold fs-16 text-white">Data Perlengkapan</h5>
                            </div>
                            <div class="card-body">
                                <div class="row g-4">
                                    <div class="col-md-4">
                                        <label class="form-label">Ukuran Seragam</label>
                                        <input type="text" class="form-control" name="ukuran_seragam" placeholder="Ukuran Seragam" value="<?php echo e(old('ukuran_seragam', ($data->ukuran_seragam ?? null))); ?>">
                                    </div>
                                    <div class="col-md-4">
                                        <label class="form-label">Ukuran Sendal</label>
                                        <input type="text" class="form-control" name="ukuran_sendal" placeholder="Ukuran Sendal" value="<?php echo e(old('ukuran_sendal', ($data->ukuran_sendal ?? null))); ?>">
                                    </div>
                                    <div class="col-md-4">
                                        <label class="form-label">Ukuran Kaos</label>
                                        <input type="text" class="form-control" name="ukuran_kaos" placeholder="Ukuran Kaos" value="<?php echo e(old('ukuran_kaos', ($data->ukuran_kaos ?? null))); ?>">
                                    </div>
                                    <div class="col-md-4">
                                        <label class="form-label">Jumlah Seragam</label>
                                        <input type="number" class="form-control" name="jumlah_seragam" placeholder="Jumlah Seragam" min="0" value="<?php echo e(old('jumlah_seragam', ($data->jumlah_seragam ?? null))); ?>">
                                    </div>
                                    <div class="col-md-4">
                                        <label class="form-label">Nomer Loker</label>
                                        <input type="text" class="form-control" name="nomer_loker" placeholder="Nomer Loker" value="<?php echo e(old('nomer_loker', ($data->nomer_loker ?? null))); ?>">
                                    </div>
                                    <div class="col-md-4">
                                        <label class="form-label">Kendaraan</label>
                                        <input type="text" class="form-control" name="kendaraan" placeholder="Kendaraan" value="<?php echo e(old('kendaraan', ($data->kendaraan ?? null))); ?>">
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Data Deposit Section -->
                        <div class="card mb-4">
                            <div class="card-header py-3 bg-dark">
                                <h5 class="mb-0 fw-bold fs-16 text-white">Data Deposit</h5>
                            </div>
                            <div class="card-body">
                                <div class="row g-4">
                                    <div class="col-md-3">
                                        <label class="form-label">Deposit Seragam</label>
                                        <input type="number" class="form-control" name="deposit_seragam" placeholder="Deposit Seragam" step="0.01" min="0" value="<?php echo e(old('deposit_seragam', ($data->deposit_seragam ?? null))); ?>">
                                    </div>
                                    <div class="col-md-3">
                                        <label class="form-label">Tanggal Deposit</label>
                                        <input type="date" class="form-control" name="tanggal_deposit" value="<?php echo e(old('tanggal_deposit', (($data->tanggal_deposit ?? null) ? $data->tanggal_deposit->format('Y-m-d') : null))); ?>">
                                    </div>
                                    <div class="col-md-3">
                                        <label class="form-label">Pengembalian Seragam</label>
                                        <input type="number" class="form-control" name="pengembalian_seragam" placeholder="Pengembalian Seragam" step="0.01" min="0" value="<?php echo e(old('pengembalian_seragam', ($data->pengembalian_seragam ?? null))); ?>">
                                    </div>
                                    <div class="col-md-3">
                                        <label class="form-label">Tanggal Pengembalian Deposit</label>
                                        <input type="date" class="form-control" name="tanggal_pengembalian_deposit" value="<?php echo e(old('tanggal_pengembalian_deposit', (($data->tanggal_pengembalian_deposit ?? null)? $data->tanggal_pengembalian_deposit->format('Y-m-d') : null))); ?>">
                                    </div>
                                </div>
                            </div>
                        </div>



                        <!-- Custom Fields Section -->
                        <div class="card mb-4">
                            <div class="card-header py-3" style="background-color: #4a90a4;">
                                <h5 class="mb-0 fw-bold fs-16 text-white">Data Lain</h5>
                                <button type="button" class="btn btn-sm btn-light float-end" id="addCustomField">
                                    <i class="feather-plus me-1"></i> Tambah Field
                                </button>
                            </div>
                            <div class="card-body">
                                <div id="customFieldsContainer">
                                    <?php
                                        $existingCustomFields = [];
                                        if (isset($data) && $data->customFields) {
                                            foreach ($data->customFields as $field) {
                                                $existingCustomFields[$field->field_name] = $field->field_value;
                                            }
                                        }

                                        // Fallback to old static fields if no dynamic fields exist
                                        if (empty($existingCustomFields) && isset($data)) {
                                            for ($i = 1; $i <= 8; $i++) {
                                                $fieldName = 'custom_field_' . $i;
                                                if ($data->$fieldName) {
                                                    $existingCustomFields['Custom Field ' . $i] = $data->$fieldName;
                                                }
                                            }
                                        }
                                    ?>

                                    <?php if(!empty($existingCustomFields)): ?>
                                        <?php $__currentLoopData = $existingCustomFields; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $fieldName => $fieldValue): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <div class="row g-3 mb-3 custom-field-row">
                                                <div class="col-md-4">
                                                    <input type="text" class="form-control" name="custom_field_names[]" placeholder="Nama Field" value="<?php echo e($fieldName); ?>">
                                                </div>
                                                <div class="col-md-7">
                                                    <textarea class="form-control" name="custom_field_values[]" rows="2" placeholder="Nilai Field"><?php echo e($fieldValue); ?></textarea>
                                                </div>
                                                <div class="col-md-1">
                                                    <button type="button" class="btn btn-danger btn-sm remove-custom-field">
                                                        <i class="feather-trash-2"></i>
                                                    </button>
                                                </div>
                                            </div>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    <?php else: ?>
                                        <div class="row g-3 mb-3 custom-field-row">
                                            <div class="col-md-4">
                                                <input type="text" class="form-control" name="custom_field_names[]" placeholder="Nama Field">
                                            </div>
                                            <div class="col-md-7">
                                                <textarea class="form-control" name="custom_field_values[]" rows="2" placeholder="Nilai Field"></textarea>
                                            </div>
                                            <div class="col-md-1">
                                                <button type="button" class="btn btn-danger btn-sm remove-custom-field">
                                                    <i class="feather-trash-2"></i>
                                                </button>
                                            </div>
                                        </div>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>

                        <div class="d-flex align-items-center gap-2 page-header-right-items-wrapper">
                            <a href="<?php echo e(route('employee.index')); ?>" class="btn btn-outline-secondary">
                                <span>Batal</span>
                            </a>
                            <button type="button" class="btn btn-primary btnSubmit">
                                <span>Simpan</span>
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <?php $__env->startPush('scripts'); ?>
        <script>
            $(document).ready(function() {
                // Initialize Select2
                $('.select2').select2({
                    theme: 'bootstrap-5',
                    width: '100%',
                    placeholder: function() {
                        return $(this).data('placeholder') || 'Pilih...';
                    },
                    allowClear: true
                });

                // Initialize Select2 for multiple hari off
                $('select[name="hari_off[]"]').select2({
                    theme: 'bootstrap-5',
                    width: '100%',
                    placeholder: 'Pilih hari off (optional)',
                    allowClear: true,
                    closeOnSelect: false
                });

                // Form submission handler
                $('.btnSubmit').on('click', function() {
                    // Basic validation
                    const nama = $('input[name="nama"]').val();
                    const status = $('select[name="status"]').val();

                    if (!nama.trim()) {
                        Swal.fire({
                            icon: 'error',
                            title: 'Nama Diperlukan!',
                            text: 'Silakan masukkan nama karyawan.'
                        });
                        return false;
                    }

                    if (!status) {
                        Swal.fire({
                            icon: 'error',
                            title: 'Status Diperlukan!',
                            text: 'Silakan pilih status karyawan.'
                        });
                        return false;
                    }

                    $('#formSubmit').submit();
                });

                // Auto-generate kode from nama if kode is empty
                $('input[name="nama"]').on('input', function() {
                    const kodeField = $('input[name="kode"]');
                    if (!kodeField.val()) {
                        let nama = $(this).val();
                        let baseCode = nama.toUpperCase()
                                          .replace(/[^A-Z0-9]/g, '')
                                          .substring(0, 6);
                        if (baseCode) {
                            kodeField.val(baseCode + Math.floor(Math.random() * 100).toString().padStart(2, '0'));
                        }
                    }
                });

                // Custom Fields Management
                $('#addCustomField').on('click', function() {
                    const newField = `
                        <div class="row g-3 mb-3 custom-field-row">
                            <div class="col-md-4">
                                <input type="text" class="form-control" name="custom_field_names[]" placeholder="Nama Field">
                            </div>
                            <div class="col-md-7">
                                <textarea class="form-control" name="custom_field_values[]" rows="2" placeholder="Nilai Field"></textarea>
                            </div>
                            <div class="col-md-1">
                                <button type="button" class="btn btn-danger btn-sm remove-custom-field">
                                    <i class="feather-trash-2"></i>
                                </button>
                            </div>
                        </div>
                    `;
                    $('#customFieldsContainer').append(newField);
                });

                // Remove custom field
                $(document).on('click', '.remove-custom-field', function() {
                    if ($('.custom-field-row').length > 1) {
                        $(this).closest('.custom-field-row').remove();
                    } else {
                        Swal.fire({
                            icon: 'warning',
                            title: 'Peringatan!',
                            text: 'Minimal harus ada satu custom field.'
                        });
                    }
                });
            });
        </script>
    <?php $__env->stopPush(); ?>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $attributes = $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $component = $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php /**PATH /Users/<USER>/ProjectWork/hris-system/resources/views/backoffice/employee/create-update.blade.php ENDPATH**/ ?>