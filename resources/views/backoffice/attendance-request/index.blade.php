<x-app-layout>
    <x-slot name="header">
        Pengaju<PERSON> A<PERSON>
    </x-slot>
    <x-slot name="headerRight">
    </x-slot>

    <div class="row">
        <div class="col-lg-12">
            <div class="card stretch stretch-full">
                <div class="card-body">
                    <div class="mb-4">
                        <div class="d-flex gap-2 align-items-center justify-content-between">
                            <a href="#" class="btn btn-outline-success" id="exportBtn">
                                <i class="feather-download me-2"></i>
                                <span>Export Excel</span>
                            </a>
                            @if (canPermission('Pengajuan Absen.Create'))
                                <a href="{{ route('attendance-request.create') }}" class="btn btn-primary">
                                    <i class="feather-plus me-2"></i>
                                    <span>Tambah Pengajuan</span>
                                </a>
                            @endif
                        </div>
                    </div>
                    <div class="d-flex align-items-center flex-wrap">
                        <div class="" style="width: 300px">
                            <input type="text" class="form-control" id="search-input"
                                placeholder="Cari nama karyawan, cabang, keterangan..." oninput="handleSearch(event)"
                                style="width: 100%" />
                        </div>
                        <div class="ms-2" style="width: 150px">
                            <select class="form-select select2" id="branch-filter">
                                <option value="">Semua Cabang</option>
                                @foreach ($branches as $branch)
                                    <option value="{{ $branch->id }}">{{ $branch->name }}</option>
                                @endforeach
                            </select>
                        </div>
                        <div class="ms-2" style="width: 150px">
                            <select class="form-select select2" id="status-filter">
                                <option value="">Semua Status</option>
                                <option value="pending">Menunggu</option>
                                <option value="approved">Disetujui</option>
                                <option value="rejected">Ditolak</option>
                            </select>
                        </div>
                        <div class="ms-2" style="width: 150px">
                            <input type="date" class="form-control" id="date-from" placeholder="Tanggal Dari">
                        </div>
                        <div class="ms-2" style="width: 150px">
                            <input type="date" class="form-control" id="date-to" placeholder="Tanggal Sampai">
                        </div>
                    </div>

                    <div class="alert alert-info mt-3">
                        <h6 class="alert-heading">Informasi Pengajuan Absen:</h6>
                        <ul class="mb-0">
                            <li>Pengajuan absen dapat dibuat untuk karyawan di cabang yang dapat diakses</li>
                            <li>Status: <span class="badge bg-warning">Menunggu</span> <span
                                    class="badge bg-success">Disetujui</span> <span
                                    class="badge bg-danger">Ditolak</span></li>
                            <li>Pengajuan yang sudah diproses tidak dapat diedit atau dihapus</li>
                            <li>Lampiran bersifat opsional dan mendukung format JPG, PNG, PDF, DOC, DOCX</li>
                        </ul>
                    </div>

                    <div class="d-flex align-items-center gap-3 mt-4">
                        <div class="bulk-actions" style="display: none">
                            <button type="button" class="btn btn-success btn-sm py-3" id="approveSelected">
                                <i class="feather-check me-1"></i>
                                <span>Setujui Terpilih</span>
                            </button>
                            <button type="button" class="btn btn-warning btn-sm py-3" id="rejectSelected">
                                <i class="feather-x me-1"></i>
                                <span>Tolak Terpilih</span>
                            </button>
                            <button type="button" class="btn btn-danger btn-sm py-3" id="deleteSelected">
                                <i class="feather-trash-2 me-1"></i>
                                <span>Hapus Terpilih</span>
                            </button>
                        </div>
                    </div>

                    <div class="table-responsive mt-4">
                        <table class="table table-hover" id="example">
                            <thead>
                                <tr>
                                    <th style="width: 12px">
                                        <input type="checkbox" class="form-check-input" id="selectAll">
                                    </th>
                                    <th style="width: 12px">No</th>
                                    <th>Tanggal Pengajuan</th>
                                    <th>Data Karyawan</th>
                                    <th>Cabang</th>
                                    <th>Tanggal Absen</th>
                                    <th>Jenis Absen</th>
                                    <th>Alasan Pengajuan</th>
                                    <th>Status</th>
                                    <th>Diajukan Oleh</th>
                                    <th>Aksi</th>
                                </tr>
                            </thead>
                            <tbody>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Rejection Modal -->
    @push('modals')
        <div class="modal fade" id="rejectModal" tabindex="-1" aria-labelledby="rejectModalLabel" aria-hidden="true">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="rejectModalLabel">Tolak Pengajuan Absen</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <form id="rejectForm">
                        <div class="modal-body">
                            <div class="mb-3">
                                <label for="rejection_reason" class="form-label">Alasan Penolakan <span
                                        class="text-danger">*</span></label>
                                <textarea class="form-control" id="rejection_reason" name="rejection_reason" rows="4"
                                    placeholder="Masukkan alasan penolakan..." required></textarea>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Batal</button>
                            <button type="submit" class="btn btn-danger">Tolak Pengajuan</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    @endpush

    @push('scripts')
        @include('libs.datatable')
        <script>
            let table;
            let searchTimeout;

            function handleSearch(event) {
                clearTimeout(searchTimeout);
                searchTimeout = setTimeout(function() {
                    table.ajax.reload();
                }, 500);
            }

            $(document).ready(function() {
                table = $('#example').DataTable({
                    lengthMenu: [
                        [10, 25, 50, 100, 500, -1],
                        [10, 25, 50, 100, 500, "All"],
                    ],
                    searching: false,
                    responsive: false,
                    lengthChange: true,
                    autoWidth: false,
                    order: [],
                    pagingType: "full_numbers",
                    dom: '<"top"f>rt<"mt-4 d-flex align-items-center justify-content-between"ilp><"clear">',
                    language: {
                        search: "_INPUT_",
                        searchPlaceholder: "Cari...",
                        paginate: {
                            Search: '<i class="icon-search"></i>',
                            first: "<i class='fas fa-angle-double-left'></i>",
                            previous: "<i class='fas fa-angle-left'></i>",
                            next: "<i class='fas fa-angle-right'></i>",
                            last: "<i class='fas fa-angle-double-right'></i>",
                        },
                    },
                    oLanguage: {
                        sSearch: "",
                    },
                    processing: true,
                    serverSide: true,
                    ajax: {
                        url: "{{ route('attendance-request.dataTable') }}",
                        type: "POST",
                        data: function(d) {
                            d.keyword = $('#search-input').val();
                            d.branch_id = $('#branch-filter').val();
                            d.status = $('#status-filter').val();
                            d.date_from = $('#date-from').val();
                            d.date_to = $('#date-to').val();
                        }
                    },
                    columns: [{
                            data: 'checkbox',
                            name: 'checkbox',
                            orderable: false,
                            searchable: false
                        },
                        {
                            data: 'DT_RowIndex',
                            name: 'DT_RowIndex',
                            orderable: false,
                            searchable: false
                        },
                        {
                            data: 'created_at',
                            name: 'created_at'
                        },
                        {
                            data: 'employee_name',
                            name: 'employeeUser.nama'
                        },
                        {
                            data: 'branch_name',
                            name: 'branch.name'
                        },
                        {
                            data: 'attendance_date_formatted',
                            name: 'attendance_date'
                        },
                        {
                            data: 'attendance_type_name',
                            name: 'attendanceType.name'
                        },
                        {
                            data: 'reason',
                            name: 'reason',
                            render: function(data, type, row) {
                                if (data && data.length > 50) {
                                    return '<span title="' + data + '">' + data.substring(0, 50) + '...</span>';
                                }
                                return data || '-';
                            }
                        },
                        {
                            data: 'status_badge',
                            name: 'status'
                        },
                        {
                            data: 'created_by',
                            name: 'user.nama'
                        },
                        {
                            data: 'action',
                            name: 'action',
                            orderable: false,
                            searchable: false,
                            class: "position-sticky top-0 right-0 bg-white-smoke"
                        }
                    ],
                });

                // Initialize Select2
                $('.select2').select2({
                    theme: 'bootstrap-5',
                    width: '100%'
                });

                // Filter change events
                $('#branch-filter, #status-filter, #date-from, #date-to').change(function() {
                    table.ajax.reload();
                });

                // Export functionality
                $('#exportBtn').click(function(e) {
                    e.preventDefault();
                    let params = new URLSearchParams();

                    if ($('#search-input').val()) params.append('keyword', $('#search-input').val());
                    if ($('#branch-filter').val()) params.append('branch_id', $('#branch-filter').val());
                    if ($('#status-filter').val()) params.append('status', $('#status-filter').val());
                    if ($('#date-from').val()) params.append('date_from', $('#date-from').val());
                    if ($('#date-to').val()) params.append('date_to', $('#date-to').val());

                    window.location.href = "{{ route('attendance-request.exportExcel') }}?" + params.toString();
                });

                // Select all functionality
                $('#selectAll').change(function() {
                    $('.select-item').prop('checked', this.checked);
                    toggleBulkActions();
                });

                $(document).on('change', '.select-item', function() {
                    toggleBulkActions();
                });

                function toggleBulkActions() {
                    let checkedItems = $('.select-item:checked').length;
                    if (checkedItems > 0) {
                        $('.bulk-actions').show();
                    } else {
                        $('.bulk-actions').hide();
                    }
                }

                // Approve functionality
                $(document).on('click', '.approveData', function() {
                    let id = $(this).data('id');

                    Swal.fire({
                        title: 'Konfirmasi',
                        text: 'Apakah Anda yakin ingin menyetujui pengajuan ini?',
                        icon: 'question',
                        showCancelButton: true,
                        confirmButtonColor: '#28a745',
                        cancelButtonColor: '#6c757d',
                        confirmButtonText: 'Ya, Setujui',
                        cancelButtonText: 'Batal'
                    }).then((result) => {
                        if (result.value) {
                            $.ajax({
                                url: "{{ url('attendance-request') }}/" + id + "/approve",
                                type: 'POST',
                                data: {
                                    _token: "{{ csrf_token() }}"
                                },
                                success: function(response) {
                                    if (response.status) {
                                        Swal.fire('Berhasil!', response.message, 'success');
                                        table.ajax.reload();
                                    } else {
                                        Swal.fire('Error!', response.message, 'error');
                                    }
                                },
                                error: function(xhr) {
                                    Swal.fire('Error!',
                                        'Terjadi kesalahan saat memproses permintaan',
                                        'error');
                                }
                            });
                        }
                    });
                });

                // Reject functionality
                let rejectId = null;
                $(document).on('click', '.rejectData', function() {
                    rejectId = $(this).data('id');
                    $('#rejectModal').modal('show');
                });

                $('#rejectForm').submit(function(e) {
                    e.preventDefault();

                    $.ajax({
                        url: "{{ url('attendance-request') }}/" + rejectId + "/reject",
                        type: 'POST',
                        data: {
                            _token: "{{ csrf_token() }}",
                            rejection_reason: $('#rejection_reason').val()
                        },
                        success: function(response) {
                            if (response.status) {
                                $('#rejectModal').modal('hide');
                                $('#rejectForm')[0].reset();
                                Swal.fire('Berhasil!', response.message, 'success');
                                table.ajax.reload();
                            } else {
                                Swal.fire('Error!', response.message, 'error');
                            }
                        },
                        error: function(xhr) {
                            Swal.fire('Error!', 'Terjadi kesalahan saat memproses permintaan',
                                'error');
                        }
                    });
                });

                // Delete functionality
                $(document).on('click', '.deleteData', function() {
                    let id = $(this).data('id');

                    Swal.fire({
                        title: 'Konfirmasi',
                        text: 'Apakah Anda yakin ingin menghapus pengajuan ini?',
                        icon: 'warning',
                        showCancelButton: true,
                        confirmButtonColor: '#dc3545',
                        cancelButtonColor: '#6c757d',
                        confirmButtonText: 'Ya, Hapus',
                        cancelButtonText: 'Batal'
                    }).then((result) => {
                        if (result.value) {
                            $.ajax({
                                url: "{{ url('attendance-request') }}/" + id,
                                type: 'DELETE',
                                data: {
                                    _token: "{{ csrf_token() }}"
                                },
                                success: function(response) {
                                    if (response.status) {
                                        Swal.fire('Berhasil!', response.message, 'success');
                                        table.ajax.reload();
                                    } else {
                                        Swal.fire('Error!', response.message, 'error');
                                    }
                                },
                                error: function(xhr) {
                                    Swal.fire('Error!',
                                        'Terjadi kesalahan saat menghapus data', 'error'
                                    );
                                }
                            });
                        }
                    });
                });

                // Bulk approve functionality
                $('#approveSelected').click(function() {
                    let selectedIds = [];
                    $('.select-item:checked').each(function() {
                        selectedIds.push($(this).val());
                    });

                    if (selectedIds.length === 0) {
                        Swal.fire('Peringatan!', 'Pilih minimal satu data untuk disetujui', 'warning');
                        return;
                    }

                    Swal.fire({
                        title: 'Konfirmasi',
                        text: `Apakah Anda yakin ingin menyetujui ${selectedIds.length} pengajuan yang dipilih?`,
                        icon: 'question',
                        showCancelButton: true,
                        confirmButtonColor: '#28a745',
                        cancelButtonColor: '#6c757d',
                        confirmButtonText: 'Ya, Setujui',
                        cancelButtonText: 'Batal'
                    }).then((result) => {
                        if (result.isConfirmed) {
                            $.ajax({
                                url: "{{ route('attendance-request.bulkApprove') }}",
                                type: 'POST',
                                data: {
                                    _token: "{{ csrf_token() }}",
                                    ids: selectedIds
                                },
                                success: function(response) {
                                    if (response.status) {
                                        Swal.fire('Berhasil!', response.message, 'success');
                                        table.ajax.reload();
                                        $('#selectAll').prop('checked', false);
                                        $('.bulk-actions').hide();
                                    } else {
                                        Swal.fire('Error!', response.message, 'error');
                                    }
                                },
                                error: function(xhr) {
                                    Swal.fire('Error!', 'Terjadi kesalahan saat memproses permintaan', 'error');
                                }
                            });
                        }
                    });
                });

                // Bulk reject functionality
                $('#rejectSelected').click(function() {
                    let selectedIds = [];
                    $('.select-item:checked').each(function() {
                        selectedIds.push($(this).val());
                    });

                    if (selectedIds.length === 0) {
                        Swal.fire('Peringatan!', 'Pilih minimal satu data untuk ditolak', 'warning');
                        return;
                    }

                    Swal.fire({
                        title: 'Tolak Pengajuan',
                        input: 'textarea',
                        inputLabel: 'Alasan Penolakan',
                        inputPlaceholder: 'Masukkan alasan penolakan...',
                        inputValidator: (value) => {
                            if (!value) {
                                return 'Alasan penolakan harus diisi!'
                            }
                        },
                        showCancelButton: true,
                        confirmButtonColor: '#dc3545',
                        cancelButtonColor: '#6c757d',
                        confirmButtonText: 'Tolak',
                        cancelButtonText: 'Batal'
                    }).then((result) => {
                        if (result.isConfirmed) {
                            $.ajax({
                                url: "{{ route('attendance-request.bulkReject') }}",
                                type: 'POST',
                                data: {
                                    _token: "{{ csrf_token() }}",
                                    ids: selectedIds,
                                    rejection_reason: result.value
                                },
                                success: function(response) {
                                    if (response.status) {
                                        Swal.fire('Berhasil!', response.message, 'success');
                                        table.ajax.reload();
                                        $('#selectAll').prop('checked', false);
                                        $('.bulk-actions').hide();
                                    } else {
                                        Swal.fire('Error!', response.message, 'error');
                                    }
                                },
                                error: function(xhr) {
                                    Swal.fire('Error!', 'Terjadi kesalahan saat memproses permintaan', 'error');
                                }
                            });
                        }
                    });
                });

                // Bulk delete functionality
                $('#deleteSelected').click(function() {
                    let selectedIds = [];
                    $('.select-item:checked').each(function() {
                        selectedIds.push($(this).val());
                    });

                    if (selectedIds.length === 0) {
                        Swal.fire('Peringatan!', 'Pilih minimal satu data untuk dihapus', 'warning');
                        return;
                    }

                    Swal.fire({
                        title: 'Konfirmasi',
                        text: `Apakah Anda yakin ingin menghapus ${selectedIds.length} pengajuan yang dipilih?`,
                        icon: 'warning',
                        showCancelButton: true,
                        confirmButtonColor: '#dc3545',
                        cancelButtonColor: '#6c757d',
                        confirmButtonText: 'Ya, Hapus',
                        cancelButtonText: 'Batal'
                    }).then((result) => {
                        if (result.value) {
                            $.ajax({
                                url: "{{ route('attendance-request.bulkDelete') }}",
                                type: 'POST',
                                data: {
                                    _token: "{{ csrf_token() }}",
                                    ids: selectedIds
                                },
                                success: function(response) {
                                    if (response.status) {
                                        Swal.fire('Berhasil!', response.message, 'success');
                                        table.ajax.reload();
                                        $('#selectAll').prop('checked', false);
                                        $('.bulk-actions').hide();
                                    } else {
                                        Swal.fire('Error!', response.message, 'error');
                                    }
                                },
                                error: function(xhr) {
                                    Swal.fire('Error!',
                                        'Terjadi kesalahan saat menghapus data', 'error'
                                    );
                                }
                            });
                        }
                    });
                });
            });
        </script>
    @endpush
</x-app-layout>
