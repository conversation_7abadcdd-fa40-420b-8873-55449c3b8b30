<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('attendance_logs', function (Blueprint $table) {
            $table->uuid('id');
            $table->string('cabang')->index(); // Branch name from Excel
            $table->string('nama')->index(); // Employee name from Excel
            $table->string('departemen')->nullable(); // Department from Excel
            $table->date('date_change')->index(); // Date from Excel
            $table->time('time_masuk')->nullable(); // Time in from Excel
            $table->time('time_keluar')->nullable(); // Time out from Excel
            
            // Optional: Add foreign key relationships if needed
            $table->uuid('user_id')->nullable()->index(); // Link to users table if name matches
            $table->uuid('branch_id')->nullable()->index(); // Link to branches table if cabang matches
            
            $table->timestamps();
            
            // Composite unique index to prevent duplicates for same employee on same date
            $table->unique(['nama', 'cabang', 'date_change'], 'unique_attendance_log');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('attendance_logs');
    }
};
