<?php $attributes ??= new \Illuminate\View\ComponentAttributeBag; ?>
<?php foreach($attributes->onlyProps(['class_container', 'class_content', 'class_main_content', 'show_footer', 'hide_header', 'hide_sidebar']) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
} ?>
<?php $attributes = $attributes->exceptProps(['class_container', 'class_content', 'class_main_content', 'show_footer', 'hide_header', 'hide_sidebar']); ?>
<?php foreach (array_filter((['class_container', 'class_content', 'class_main_content', 'show_footer', 'hide_header', 'hide_sidebar']), 'is_string', ARRAY_FILTER_USE_KEY) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
} ?>
<?php $__defined_vars = get_defined_vars(); ?>
<?php foreach ($attributes as $__key => $__value) {
    if (array_key_exists($__key, $__defined_vars)) unset($$__key);
} ?>
<?php unset($__defined_vars); ?>
<!DOCTYPE html>
<html lang="<?php echo e(str_replace('_', '-', app()->getLocale())); ?>">

<head>
    <meta charset="utf-8" />
    <meta http-equiv="x-ua-compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta name="description" content="" />
    <meta name="keyword" content="" />
    <meta name="author" content="theme_ocean" />
    <meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">
    <title>Anna Holding</title>
    <link rel="shortcut icon" type="image/x-icon" href="/assets/images/favicon.ico" />
    <link rel="stylesheet" type="text/css" href="/assets/css/bootstrap.min.css" />
    <link rel="stylesheet" type="text/css" href="/assets/vendors/css/vendors.min.css" />
    <link rel="stylesheet" type="text/css" href="/assets/css/theme.min.css" />
    <link rel="stylesheet" type="text/css" href="/assets/vendors/css/select2.min.css">
    <link rel="stylesheet" type="text/css" href="/assets/vendors/css/select2-theme.min.css">
    <link rel="stylesheet" type="text/css" href="/assets/vendors/css/daterangepicker.min.css" />
    <?php echo $__env->yieldPushContent('styles'); ?>
    <?php echo app('Illuminate\Foundation\Vite')(['resources/css/app.css', 'resources/js/app.js', 'resources/js/helper.js']); ?>
    <?php echo $__env->yieldPushContent('push_data_script'); ?>
    <style>
        <?php if($hide_sidebar ?? false): ?>
            .nxl-container {
                margin-left: 0 !important;
            }

            .items-details-footer {
                margin: 0 !important;
                padding: 0 !important;
            }
        <?php endif; ?>
    </style>
</head>

<body>
    <?php if(!($hide_sidebar ?? false)): ?>
        <?php echo $__env->make('layouts.sidebar', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
    <?php endif; ?>
    <?php if(!($hide_header ?? false)): ?>
        <?php echo $__env->make('layouts.navbar', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
    <?php endif; ?>
    <main class="nxl-container <?php echo e($class_container ?? ''); ?> <?php echo e(!($hide_header ?? false) ? '' : 'hide__navbar'); ?>"">
        <div class="nxl-content <?php echo e($class_content ?? ''); ?>">
            <?php if(isset($header)): ?>
                <?php if (isset($component)) { $__componentOriginalfd1f218809a441e923395fcbf03e4272 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalfd1f218809a441e923395fcbf03e4272 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.header','data' => ['headerRight' => $headerRight ?? '']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('header'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['headerRight' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($headerRight ?? '')]); ?>
                    <?php echo e($header); ?>

                 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalfd1f218809a441e923395fcbf03e4272)): ?>
<?php $attributes = $__attributesOriginalfd1f218809a441e923395fcbf03e4272; ?>
<?php unset($__attributesOriginalfd1f218809a441e923395fcbf03e4272); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalfd1f218809a441e923395fcbf03e4272)): ?>
<?php $component = $__componentOriginalfd1f218809a441e923395fcbf03e4272; ?>
<?php unset($__componentOriginalfd1f218809a441e923395fcbf03e4272); ?>
<?php endif; ?>
            <?php endif; ?>
            <div class="main-content <?php echo e($class_main_content ?? ''); ?>" id="app">
                <?php echo e($slot); ?>

            </div>
        </div>
        <?php if($show_footer ?? false): ?>
            <footer class="footer">
                <p class="fs-11 text-muted fw-medium text-uppercase mb-0 copyright">
                    <span>Copyright ©</span>
                    <script>
                        document.write(new Date().getFullYear());
                    </script>
                </p>
            </footer>
        <?php endif; ?>

    </main>

    <?php echo $__env->yieldPushContent('modals'); ?>

    <script src="/assets/vendors/js/vendors.min.js"></script>
    <script src="/assets/js/common-init.min.js"></script>
    <script src="/assets/vendors/js/sweetalert2.min.js"></script>
    <script src="/assets/js/helper.js"></script>
    <script src="/assets/vendors/js/select2.min.js"></script>
    <script src="/assets/vendors/js/select2-active.min.js"></script>
    <script src="/assets/vendors/js/daterangepicker.min.js"></script>
    <script>
        $.ajaxSetup({
            headers: {
                "X-CSRF-TOKEN": $("meta[name='csrf-token']").attr("content"),
            },
        });
        const base_url = '<?php echo e(url('/')); ?>';
        const csrf = '<?php echo e(csrf_token()); ?>';
        $('.select2').not('.day-type-select').select2({
            theme: "bootstrap-5",
            width: $(this).data('width') ? $(this).data('width') : $(this).hasClass('w-100') ? '100%' : 'style',
            placeholder: $(this).data('placeholder'),
            allowClear: Boolean($(this).data('allow-clear')),
        });

        $('#sessionUnitBisnisId').on('change', function() {
            $('#change-unit-bisnis').submit();
        });

        const notificationList = $('#notificationList');
        const countNoReadItem = $('#countNoRead');
        const refreshNotificationBtn = $('.refreshNotification');
        const makeAsRead = $('.makeAsRead');

        let notificationData = [];
        let loadingNotification = false;
        let notificationPagination = {
            page: 1,
            last_page: 1
        }
        let countNoRead = 0;
        const getNotification = async () => {
            if (loadingNotification) {
                return;
            }

            if (notificationPagination.page > notificationPagination.last_page) {
                return;
            }

            loadingNotification = true;

            listNotification = `
                <div class="notifications-item">
                    <div class="notifications-desc text-center">
                        <a href="javascript:void(0);" class="font-body text-truncate-2-line">
                            Loading...
                        </a>
                    </div>
                </div>
            `;
            notificationList.append(listNotification);

            const {
                data
            } = await axios.get(`/notification/list`, {
                headers: {
                    'X-CSRF-TOKEN': csrf
                },
                params: {
                    page: notificationPagination.page
                }
            });
            notificationData = [...notificationData, ...data?.data?.data];
            loadingNotification = false;
            notificationPagination = {
                page: data?.data?.current_page,
                last_page: data?.data?.last_page
            }
            generateListNotification();
        }

        const getCountNoRead = async () => {
            const {
                data
            } = await axios.get(`/notification/count-no-read`, {
                headers: {
                    'X-CSRF-TOKEN': csrf
                }
            });
            countNoRead = data?.data;
            countNoReadElement();
        }

        const generateListNotification = () => {
            let listNotification = ''
            if (notificationData.length > 0) {
                listNotification = notificationData?.map((item) => {
                    return `
                    <div class="notifications-item" style="${!item?.is_read ? 'background-color: rgb(234, 235, 239);' : ''}">
                        <div class="notifications-desc">
                            <a href="/surat-masuk/${item?.surat_id}/detail" class="font-body text-truncate-2-line">
                                ${item.message}
                            </a>
                            <div class="d-flex justify-content-between align-items-center">
                                <div class="notifications-date text-muted border-bottom border-bottom-dashed">
                                    ${item.diff_for_humans}
                                </div>
                            </div>
                        </div>
                    </div>
                `;
                });
                listNotification = listNotification.join('');
            } else {
                listNotification = `
                <div class="notifications-item">
                    <div class="notifications-desc text-center">
                        <a href="javascript:void(0);" class="font-body text-truncate-2-line">
                            No Notification
                        </a>
                    </div>
                </div>
                `;
            }

            notificationList.html(listNotification);
        }

        const countNoReadElement = () => {
            countNoReadItem.text(countNoRead);
        }

        notificationList.on('scroll', function() {
            if (notificationList.scrollTop() + notificationList.innerHeight() >= notificationList[0].scrollHeight) {
                notificationPagination.page += 1;
                getNotification();
            }
        });

        const refreshNotification = () => {
            notificationData = [];
            notificationPagination = {
                page: 1,
                last_page: 1
            }
            getCountNoRead();
            getNotification();
        }

        refreshNotificationBtn.on('click', function() {
            refreshNotification();
        });

        makeAsRead.on('click', async function() {
            if (countNoRead === 0) {
                return;
            }

            const {
                data
            } = await axios.post(`/notification/make-as-read`, {}, {
                headers: {
                    'X-CSRF-TOKEN': csrf
                }
            });
            if (data?.status) {
                refreshNotification()
                Swal.fire({
                    icon: 'success',
                    title: 'Berhasil',
                    text: data?.message,
                });
            }
        });


        $(() => {
            getNotification();
            getCountNoRead();
        })
    </script>
    <?php echo $__env->yieldPushContent('scripts'); ?>
</body>

</html>
<?php /**PATH /Users/<USER>/ProjectWork/hris-system/resources/views/layouts/app.blade.php ENDPATH**/ ?>