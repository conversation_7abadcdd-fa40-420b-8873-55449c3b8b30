<x-app-layout>
    <x-slot name="header">
        Pengguna
    </x-slot>
    <x-slot name="headerRight">

    </x-slot>

        <div class="row">
            <div class="col-lg-12">
                <div class="card stretch stretch-full">
                    <div class="card-body">
                        <div class="d-flex align-items-center flex-wrap">
                            <div class="" style="width: 300px">
                                <input type="text" class="form-control" placeholder="Cari nama user/unit/role"
                                    oninput="handleSearch(event)"
                                    style="width: 100%" />
                            </div>
                            <div class="ms-md-auto mt-md-0 mt-3">
                                @if (canPermission('Pengguna.Create'))
                                <div>
                                    <a href="{{ route('pengguna.create') }}" class="btn btn-primary">
                                        <i class="feather-plus me-2"></i>
                                        <span>Tambah Pengguna</span>
                                    </a>
                                </div>
                                @endif
                            </div>
                        </div>
                        <div class="table-responsive mt-4">
                            <table class="table table-hover" id="example">
                                <thead>
                                    <tr>
                                        <th style="width: 12px">No</th>
                                        <th>Nama</th>
                                        <th>Email</th>
                                        <th>Role</th>
                                        <th>Akses Cabang</th>
                                        <th>Aksi</th>
                                    </tr>
                                </thead>
                                <tbody>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        @include('libs.datatable')
        @push('scripts')
            <script>
                let filters = {};
                const table = $("#example").DataTable({
                    lengthMenu: [
                        [10, 25, 50, 100, 500, -1],
                        [10, 25, 50, 100, 500, "All"],
                    ],
                    searching: false,
                    responsive: false,
                    lengthChange: true,
                    autoWidth: false,
                    order: [],
                    pagingType: "full_numbers",
                    dom: '<"top"f>rt<"mt-4 d-flex align-items-center justify-content-between"ilp><"clear">',
                    language: {
                        search: "_INPUT_",
                        searchPlaceholder: "Cari...",
                        paginate: {
                            Search: '<i class="icon-search"></i>',
                            first: "<i class='fas fa-angle-double-left'></i>",
                            previous: "<i class='fas fa-angle-left'></i>",
                            next: "<i class='fas fa-angle-right'></i>",
                            last: "<i class='fas fa-angle-double-right'></i>",
                        },
                    },
                    oLanguage: {
                        sSearch: "",
                    },
                    processing: true,
                    serverSide: true,
                    ajax: {
                        url: `${base_url}/pengguna/dataTable`,
                        method: "POST",
                        data: function(d) {
                            return {
                                ...d,
                                ...filters,
                            };
                        },
                    },
                    columns: [{
                            name: "created_at",
                            data: "DT_RowIndex",
                        },
                        {
                            name: "nama",
                            data: "nama",
                            orderable: false,
                        },
                        {
                            name: "email",
                            data: "email",
                            orderable: false,
                        },
                        {
                            name: "role.name",
                            data: (data) => data?.role?.name ?? '-',
                            orderable: false,
                        },
                        {
                            name: "branches",
                            data: "branches",
                            orderable: false,
                        },
                        {
                            name: "action",
                            data: "action",
                            orderable: false,
                        },

                    ],
                });

                let debounceTimer;
                function handleSearch(e) {
                    clearTimeout(debounceTimer);
                    debounceTimer = setTimeout(() => {
                        filters.keyword = e.target.value;
                        table.draw();
                    }, 500);
                }

                $(document).on('click', '.deleteData', async function() {
                    const id = $(this).data("id");
                    const dataInput = $(this).data("input");
                    const nama = dataInput.nama;
                    const urlTarget = `${base_url}/pengguna/${id}`
                    await deleteDataTable(nama, urlTarget, table)
                });

            </script>
        @endpush
</x-app-layout>
