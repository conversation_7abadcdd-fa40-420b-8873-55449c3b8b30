<nav class="nxl-navigation">
    <div class="navbar-wrapper">
        <div class="m-header justify-content-center">
            <a href="<?php echo e(route('dashboard')); ?>" class="b-brand">
                <h5 class="fs-16 fw-bolder mb-1 text-center">Sistem <PERSON></h5>
            </a>
        </div>
        <div class="navbar-content">
            <ul class="nxl-navbar">
                <?php
                    $menus = [
                        [
                            'icon' => 'feather-airplay',
                            'name' => 'Dashboard',
                            'url' => route('dashboard'),
                            'active' => ['dashboard.index'],
                            'child' => [],
                        ],
                        [
                            'icon' => 'feather-user',
                            'name' => '<PERSON><PERSON><PERSON>',
                            'url' => route('employee.index'),
                            'active' => ['employee.index'],
                            'child' => [],
                        ],
                        [
                            'icon' => 'feather-upload',
                            'name' => 'Upload Log Absen',
                            'url' => route('attendance-log.index'),
                            'active' => ['attendance-log.index'],
                            'child' => [],
                        ],
                        [
                            'icon' => 'feather-file-text',
                            'name' => '<PERSON><PERSON><PERSON><PERSON>',
                            'url' => route('attendance-request.index'),
                            'active' => [
                                'attendance-request.index',
                                'attendance-request.create',
                                'attendance-request.edit',
                                'attendance-request.show',
                            ],
                            'child' => [],
                        ],
                        [
                            'icon' => 'feather-user',
                            'name' => 'Management',
                            'url' => '#',
                            'active' => [
                                'pengguna.index',
                                'pengguna.create',
                                'pengguna.edit',
                                'role-permission.index',
                                'role-permission.create',
                                'role-permission.edit',
                            ],
                            'child' => [
                                [
                                    'icon' => 'feather-user',
                                    'name' => 'Pengguna',
                                    'url' => route('pengguna.index'),
                                    'permission' => 'Pengguna.List',
                                    'active' => ['pengguna.index', 'pengguna.create', 'pengguna.edit'],
                                    'child' => [],
                                ],
                                [
                                    'icon' => 'feather-user',
                                    'name' => 'Peran & Hak Akses',
                                    'permission' => 'Role & Permission.List',
                                    'url' => route('role-permission.index'),
                                    'active' => [
                                        'role-permission.index',
                                        'role-permission.create',
                                        'role-permission.edit',
                                    ],
                                    'permission' => 'Role & Permission.List',
                                    'child' => [],
                                ],
                            ],
                        ],
                        [
                            'icon' => 'feather-database',
                            'name' => 'Master Data HRIS',
                            'url' => '#',
                            'active' => [
                                'branch.index',
                                'branch.create',
                                'branch.edit',
                                'leave-off-setting.index',
                                'leave-off-setting.create',
                                'leave-off-setting.edit',
                                'working-hour-setting.index',
                                'working-hour-setting.create',
                                'working-hour-setting.edit',
                                'attendance-type-setting.index',
                                'attendance-type-setting.create',
                                'attendance-type-setting.edit',
                                'working-calendar-setting.index',
                                'working-calendar-setting.create',
                                'working-calendar-setting.edit',
                                'user-access-branch.index',
                            ],
                            'child' => [
                                [
                                    'icon' => 'feather-map-pin',
                                    'name' => 'Cabang',
                                    'url' => route('branch.index'),
                                    'permission' => 'Master Cabang.List',
                                    'active' => ['branch.index', 'branch.create', 'branch.edit'],
                                    'child' => [],
                                ],
                                [
                                    'icon' => 'feather-calendar',
                                    'name' => 'Pengaturan Cuti',
                                    'url' => route('leave-off-setting.index'),
                                    'permission' => 'Master Pengaturan Cuti.List',
                                    'active' => [
                                        'leave-off-setting.index',
                                        'leave-off-setting.create',
                                        'leave-off-setting.edit',
                                    ],
                                    'child' => [],
                                ],
                                [
                                    'icon' => 'feather-clock',
                                    'name' => 'Jam Kerja',
                                    'url' => route('working-hour-setting.index'),
                                    'permission' => 'Master Jam Kerja.List',
                                    'active' => [
                                        'working-hour-setting.index',
                                        'working-hour-setting.create',
                                        'working-hour-setting.edit',
                                    ],
                                    'child' => [],
                                ],
                                [
                                    'icon' => 'feather-check-circle',
                                    'name' => 'Jenis Absen',
                                    'url' => route('attendance-type-setting.index'),
                                    'permission' => 'Master Jenis Absen.List',
                                    'active' => [
                                        'attendance-type-setting.index',
                                        'attendance-type-setting.create',
                                        'attendance-type-setting.edit',
                                    ],
                                    'child' => [],
                                ],
                                [
                                    'icon' => 'feather-calendar',
                                    'name' => 'Kalender Kerja',
                                    'url' => route('working-calendar-setting.index'),
                                    'permission' => 'Master Kalender Kerja.List',
                                    'active' => [
                                        'working-calendar-setting.index',
                                        'working-calendar-setting.create',
                                        'working-calendar-setting.edit',
                                    ],
                                    'child' => [],
                                ],
                            ],
                        ],
                        [
                            'icon' => 'feather-settings',
                            'name' => 'Pengaturan',
                            'url' => '#',
                            'active' => ['pengaturan.e-sign.index'],
                            'child' => [
                                [
                                    'icon' => '',
                                    'name' => 'Default',
                                    'url' => route('pengaturan.default.index'),
                                    'permission' => 'Pengaturan.Default',
                                    'active' => ['pengaturan.default.index'],
                                    'child' => [],
                                ],
                            ],
                        ],
                    ];
                ?>
                <li class="nxl-item nxl-caption">
                    <label>Navigation</label>
                </li>
                <?php $__currentLoopData = $menus; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <?php if(count($item['child']) > 0): ?>
                        <?php if(canPermissionMultiple(array_column($item['child'], 'permission')) ||
                                count(array_column($item['child'], 'permission')) === 0): ?>
                            <li
                                class="nxl-item nxl-hasmenu <?php echo e(set_active($item['active'] ?? [])); ?> <?php echo e(set_active($item['active'] ?? [], 'nxl-trigger')); ?>">
                                <a href="<?php echo e($item['url'] ?? ''); ?>" class="nxl-link">
                                    <span class="nxl-micon"><i class="<?php echo e($item['icon']); ?>"></i></span>
                                    <span class="nxl-mtext"><?php echo e($item['name']); ?></span>
                                    <span class="nxl-arrow"><i class="feather-chevron-right"></i></span>
                                </a>
                                <ul class="nxl-submenu">
                                    <?php $__currentLoopData = $item['child']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $child): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <?php if((isset($child['permission']) && canPermission($child['permission'])) || !isset($child['permission'])): ?>
                                            <li class="nxl-item <?php echo e(set_active($child['active'] ?? [])); ?>"><a
                                                    class="nxl-link"
                                                    href="<?php echo e($child['url'] ?? ''); ?>"><?php echo e($child['name']); ?></a>
                                            </li>
                                        <?php endif; ?>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </ul>
                            </li>
                        <?php endif; ?>
                    <?php else: ?>
                        <?php if((isset($item['permission']) && canPermission($item['permission'])) || !isset($item['permission'])): ?>
                            <li class="nxl-item <?php echo e(set_active($item['active'] ?? [])); ?>">
                                <a href="<?php echo e($item['url']); ?>" class="nxl-link">
                                    <span class="nxl-micon"><i class="<?php echo e($item['icon']); ?>"></i></span>
                                    <span class="nxl-mtext"><?php echo e($item['name']); ?></span>
                                </a>
                            </li>
                        <?php endif; ?>
                    <?php endif; ?>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </ul>
        </div>
    </div>
</nav>
<?php /**PATH /Users/<USER>/ProjectWork/hris-system/resources/views/layouts/sidebar.blade.php ENDPATH**/ ?>