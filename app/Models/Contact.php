<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Contact extends Model
{
    use HasFactory, HasUuids, SoftDeletes;
    protected $guarded = ['id'];

    public function scopeFilter($query, $request)
    {
        return $query->when($request->keyword, function ($q, $filter)  {
            $q->where('nama', 'like', '%' . $filter . '%')
                ->orWhere('email', 'like', '%' . $filter . '%');
        });
    }
}
